[18:04:16 INFO]: [BedwarsScoreBoardAddon] 检测到BedwarsXP，正在重新初始化商店兼容性设置...
[18:04:16 INFO]: [BedwarsScoreBoardAddon] BWSBA商店事件触发 - 游戏: Ver_zhzh, 玩家: Ver_zhzh, 实体: PLAYER, 事件状态: false
[18:04:16 INFO]: [BedwarsScoreBoardAddon] BWSBA处理商店打开 - 玩家: Ver_zhzh, 实体类型: PLAYER, 事件已取消: false
[18:04:16 INFO]: [BedwarsScoreBoardAddon] 检测到商店实体: PLAYER
[18:04:16 INFO]: [BedwarsScoreBoardAddon] 打开Citizens NPC物品商店
[18:04:16 INFO]: [BedwarsScoreBoardAddon] BWSBA处理后事件状态: true
[18:04:16 INFO]: [BedwarsScoreBoardAddon] BWSBA商店事件触发 - 游戏: Ver_zhzh, 玩家: Ver_zhzh, 实体: PLAYER, 事件状态: false
[18:04:16 INFO]: [BedwarsScoreBoardAddon] BWSBA处理商店打开 - 玩家: Ver_zhzh, 实体类型: PLAYER, 事件已取消: false
[18:04:16 INFO]: [BedwarsScoreBoardAddon] 检测到商店实体: PLAYER
[18:04:16 INFO]: [BedwarsScoreBoardAddon] 打开Citizens NPC物品商店
[18:04:16 INFO]: [BedwarsScoreBoardAddon] BWSBA处理后事件状态: true
[18:04:16 INFO]: [BedwarsScoreBoardAddon] 使用BedwarsXP兼容模式打开商店
[18:04:16 INFO]: [BedwarsScoreBoardAddon] 商店已打开 - 类型: XPItemShop
[18:04:16 INFO]: [BedwarsScoreBoardAddon] 使用BedwarsXP兼容模式打开商店
[18:04:16 INFO]: [BedwarsScoreBoardAddon] 商店已打开 - 类型: XPItemShop
[18:04:18 INFO]: [BedwarsScoreBoardAddon] BWSBA商店事件触发 - 游戏: Ver_zhzh, 玩家: Ver_zhzh, 实体: VILLAGER, 事件状态: false
[18:04:18 INFO]: [BedwarsScoreBoardAddon] BWSBA处理商店打开 - 玩家: Ver_zhzh, 实体类型: VILLAGER, 事件已取消: false
[18:04:18 INFO]: [BedwarsScoreBoardAddon] 检测到商店实体: VILLAGER
[18:04:18 INFO]: [BedwarsScoreBoardAddon] 打开原版村民商店
[18:04:18 INFO]: [BedwarsScoreBoardAddon] BWSBA处理后事件状态: true
[18:04:18 INFO]: [BedwarsScoreBoardAddon] 使用BedwarsXP兼容模式打开商店
[18:04:18 INFO]: [BedwarsScoreBoardAddon] 商店已打开 - 类型: XPItemShop










[18:04:39 ERROR]: [BedwarsScoreBoardAddon] Unhandled exception occurred in onPacketReceiving(PacketEvent) for BedwarsScoreBoardAddon
java.lang.IndexOutOfBoundsException: Index 215 out of bounds for length 46
at jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100) ~[?:?]
at jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106) ~[?:?]
at jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302) ~[?:?]
at java.util.Objects.checkIndex(Objects.java:385) ~[?:?]
at java.util.ArrayList.get(ArrayList.java:427) ~[?:?]
at net.minecraft.core.NonNullList.get(NonNullList.java:44) ~[paper-1.20.jar:git-Paper-17]
at net.minecraft.world.inventory.AbstractContainerMenu.getSlot(AbstractContainerMenu.java:375) ~[?:?]
at org.bukkit.craftbukkit.v1_20_R1.inventory.CraftInventoryView.getItem(CraftInventoryView.java:73) ~[paper-1.20.jar:git-Paper-17]
at me.ram.bedwarsscoreboardaddon.listener.EventListener$3.onPacketReceiving(EventListener.java:512) ~[BedwarsScoreBoardAddon-2.13.1-1.20.jar:?]
at com.comphenix.protocol.injector.collection.InboundPacketListenerSet.invokeListener(InboundPacketListenerSet.java:25) ~[ProtocolLib.jar:?]
at com.comphenix.protocol.injector.collection.PacketListenerSet.lambda$invoke$0(PacketListenerSet.java:123) ~[ProtocolLib.jar:?]
at com.comphenix.protocol.timing.TimingTracker.lambda$static$0(TimingTracker.java:7) ~[ProtocolLib.jar:?]
at com.comphenix.protocol.injector.collection.PacketListenerSet.invoke(PacketListenerSet.java:123) ~[ProtocolLib.jar:?]
at com.comphenix.protocol.injector.collection.PacketListenerSet.invoke(PacketListenerSet.java:102) ~[ProtocolLib.jar:?]
at com.comphenix.protocol.injector.PacketFilterManager.postPacketToListeners(PacketFilterManager.java:528) ~[ProtocolLib.jar:?]
at com.comphenix.protocol.injector.PacketFilterManager.invokeInboundPacketListeners(PacketFilterManager.java:509) ~[ProtocolLib.jar:?]
at com.comphenix.protocol.injector.netty.channel.NettyChannelInjector.processInboundInternal(NettyChannelInjector.java:455) ~[ProtocolLib.jar:?]
at com.comphenix.protocol.injector.netty.channel.NettyChannelInjector.processInbound(NettyChannelInjector.java:439) ~[ProtocolLib.jar:?]
at com.comphenix.protocol.injector.netty.channel.InboundPacketInterceptor.channelRead(InboundPacketInterceptor.java:47) ~[ProtocolLib.jar:?]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346) ~[netty-codec-4.1.87.Final.jar:4.1.87.Final]
at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318) ~[netty-codec-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103) ~[netty-codec-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346) ~[netty-codec-4.1.87.Final.jar:4.1.87.Final]
at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318) ~[netty-codec-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346) ~[netty-codec-4.1.87.Final.jar:4.1.87.Final]
at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318) ~[netty-codec-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.handler.timeout.IdleStateHandler.channelRead(IdleStateHandler.java:286) ~[netty-handler-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.handler.flush.FlushConsolidationHandler.channelRead(FlushConsolidationHandler.java:152) ~[netty-handler-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562) ~[netty-transport-4.1.87.Final.jar:4.1.87.Final]
at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.87.Final.jar:4.1.87.Final]
at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.87.Final.jar:4.1.87.Final]
at java.lang.Thread.run(Thread.java:1583) ~[?:?]
[18:04:39 ERROR]: Parameters:
net.minecraft.network.protocol.game.PacketPlayInWindowClick@75dd7e99[
b=0
c=215
d=-999
e=0
f=THROW
g=0 air
h={}
]
