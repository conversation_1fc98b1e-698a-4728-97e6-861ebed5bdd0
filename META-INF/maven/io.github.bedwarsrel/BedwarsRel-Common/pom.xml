<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>BedwarsRel-Parent</artifactId>
    <groupId>io.github.bedwarsrel</groupId>
    <version>1.3.6</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>BedwarsRel-Common</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.gmail.filoghost.holographicdisplays</groupId>
      <artifactId>api</artifactId>
      <version>1.0</version>
      <scope>system</scope>
      <systemPath>${pom.basedir}/../../../lib/HolographicDisplaysAPI_v2.1.7.jar</systemPath>
    </dependency>
    <dependency>
      <groupId>com.gmail.filoghost.holographicdisplays</groupId>
      <artifactId>holographicdisplays-api</artifactId>
      <version>2.1.7</version>
      <scope>system</scope>
      <systemPath>${pom.basedir}/../../../lib/HolographicDisplaysAPI_v2.1.7.jar</systemPath>
    </dependency>
    <dependency>
      <groupId>com.bugsnag</groupId>
      <artifactId>bugsnag</artifactId>
      <version>3.0.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-log4j12</artifactId>
      <version>1.7.22</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.zaxxer</groupId>
      <artifactId>HikariCP-java7</artifactId>
      <version>2.4.11</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.googlecode.json-simple</groupId>
      <artifactId>json-simple</artifactId>
      <version>1.1.1</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>


</project>