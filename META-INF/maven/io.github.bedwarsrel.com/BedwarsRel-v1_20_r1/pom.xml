<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>BedwarsRel-Parent</artifactId>
    <groupId>io.github.bedwarsrel</groupId>
    <version>1.3.6</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <groupId>io.github.bedwarsrel.com</groupId>
  <artifactId>BedwarsRel-v1_20_r1</artifactId>

  <dependencies>
    <dependency>
      <groupId>io.github.bedwarsrel</groupId>
      <artifactId>BedwarsRel-Common</artifactId>
      <version>${project.parent.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>io.papermc.paper</groupId>
      <artifactId>paper-api</artifactId>
      <version>1.20.1-R0.1-SNAPSHOT</version>
      <scope>system</scope>
      <systemPath>${pom.basedir}/../../../lib/paper-1.20-17.jar</systemPath>
    </dependency>
  </dependencies>

</project>
