package me.ram.bedwarsscoreboardaddon.listener;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityPickupItemEvent;
import org.bukkit.event.player.PlayerPickupItemEvent;

import ldcr.BedwarsXP.EventListeners;

public class XPEventListener extends EventListeners implements Listener {

	@EventHandler(ignoreCancelled = true, priority = EventPriority.HIGHEST)
	public void onItemPickup(EntityPickupItemEvent e) {
		// 检查是否是玩家拾取物品
		if (e.getEntity() instanceof Player) {
			// 由于BedwarsXP可能还在使用旧的事件系统，我们暂时不处理新事件
			// 让旧事件处理器处理兼容性
		}
	}

	// 保留旧事件的兼容性（如果BedwarsXP还在使用）
	@SuppressWarnings("deprecation")
	@EventHandler(ignoreCancelled = true, priority = EventPriority.HIGHEST)
	public void onItemPickupLegacy(PlayerPickupItemEvent e) {
		try {
			super.onItemPickup(e);
		} catch (Exception ex) {
			// 静默处理，避免错误
		}
	}
}
