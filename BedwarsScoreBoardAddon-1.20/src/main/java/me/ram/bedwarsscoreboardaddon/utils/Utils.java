package me.ram.bedwarsscoreboardaddon.utils;

import java.lang.reflect.Constructor;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import io.github.bedwarsrel.BedwarsRel;

public class Utils {

	public static void sendMessage(Player player, Player placeholderPlayer, String text) {
		player.sendMessage(PlaceholderAPIUtil.setPlaceholders(placeholderPlayer, text));
	}

	public static void sendPlayerActionbar(Player player, String text) {
		text = PlaceholderAPIUtil.setPlaceholders(player, text);
		// 使用现代化API发送ActionBar
		ModernAPIUtils.sendActionBar(player, text);
	}

	public static void sendFullTitle(Player player, Integer fadeIn, Integer stay, Integer fadeOut, String title, String subtitle) {
		sendTitle(player, fadeIn, stay, fadeOut, title, subtitle);
	}

	public static void sendPacket(Player player, Object packet) {
		// 使用现代化的兼容性工具发送数据包
		NMSCompatibility.sendPacket(player, packet);
	}

	public static Class<?> getNMSClass(String name) {
		// 使用现代化的兼容性工具获取NMS类
		return NMSCompatibility.getNMSClass(name);
	}

	public static Class<?> getClass(String name) {
		String version = Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];
		try {
			return Class.forName("org.bukkit.craftbukkit." + version + "." + name);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public static void sendTitle(Player player, Player PlaceholderPlayer, Integer fadeIn, Integer stay, Integer fadeOut, String title, String subtitle) {
		// 处理占位符
		if (title != null) {
			title = PlaceholderAPIUtil.setPlaceholders(PlaceholderPlayer, title);
		}
		if (subtitle != null) {
			subtitle = PlaceholderAPIUtil.setPlaceholders(PlaceholderPlayer, subtitle);
		}
		// 使用现代化API发送Title
		ModernAPIUtils.sendTitle(player, fadeIn, stay, fadeOut, title, subtitle);
	}

	public static void sendTitle(Player player, Integer fadeIn, Integer stay, Integer fadeOut, String title, String subtitle) {
		// 使用现代化API发送Title
		ModernAPIUtils.sendTitle(player, fadeIn, stay, fadeOut, title, subtitle);
	}

	public static void clearTitle(Player player) {
		// 使用现代化API清除Title
		ModernAPIUtils.clearTitle(player);
	}
}
