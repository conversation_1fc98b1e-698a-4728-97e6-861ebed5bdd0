package me.ram.bedwarsscoreboardaddon.utils;

import org.bukkit.Effect;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

/**
 * 材料兼容性工具类 - 处理1.20版本的材料变更
 * 
 * <AUTHOR>
 * @version 2.13.1-1.20
 */
public class MaterialCompatibility {
    
    /**
     * 获取兼容的材料
     * 
     * @param oldMaterialName 旧材料名称
     * @return 兼容的材料
     */
    public static Material getCompatibleMaterial(String oldMaterialName) {
        switch (oldMaterialName.toUpperCase()) {
            // 床方块
            case "BED_BLOCK":
                return Material.RED_BED;
            case "BED":
                return Material.RED_BED;
                
            // 羊毛
            case "WOOL":
                return Material.WHITE_WOOL;
                
            // 染色玻璃
            case "STAINED_GLASS":
                return Material.WHITE_STAINED_GLASS;
            case "STAINED_GLASS_PANE":
                return Material.WHITE_STAINED_GLASS_PANE;
                
            // 木制工具
            case "WOOD_SWORD":
                return Material.WOODEN_SWORD;
            case "WOOD_AXE":
                return Material.WOODEN_AXE;
            case "WOOD_PICKAXE":
                return Material.WOODEN_PICKAXE;
            case "WOOD_SPADE":
                return Material.WOODEN_SHOVEL;
            case "WOOD_HOE":
                return Material.WOODEN_HOE;
                
            // 金制装备
            case "GOLD_BOOTS":
                return Material.GOLDEN_BOOTS;
            case "GOLD_LEGGINGS":
                return Material.GOLDEN_LEGGINGS;
            case "GOLD_CHESTPLATE":
                return Material.GOLDEN_CHESTPLATE;
            case "GOLD_HELMET":
                return Material.GOLDEN_HELMET;
            case "GOLD_SWORD":
                return Material.GOLDEN_SWORD;
                
            // 头颅
            case "SKULL_ITEM":
                return Material.PLAYER_HEAD;
                
            // 食物
            case "RAW_FISH":
                return Material.COD;
            case "RAW_CHICKEN":
                return Material.CHICKEN;
                
            // 烟花
            case "FIREWORK":
                return Material.FIREWORK_ROCKET;
                
            // 告示牌
            case "SIGN":
                return Material.OAK_SIGN;
                
            // 矿车
            case "STORAGE_MINECART":
                return Material.CHEST_MINECART;
                
            // 火球
            case "FIREBALL":
                return Material.FIRE_CHARGE;
                
            // 栅栏
            case "IRON_FENCE":
                return Material.IRON_BARS;
                
            default:
                // 尝试直接获取材料
                Material material = Material.getMaterial(oldMaterialName);
                return material != null ? material : Material.STONE;
        }
    }
    
    /**
     * 获取兼容的效果
     * 
     * @param oldEffectName 旧效果名称
     * @return 兼容的效果
     */
    public static Effect getCompatibleEffect(String oldEffectName) {
        switch (oldEffectName.toUpperCase()) {
            case "FOOTSTEP":
                // FOOTSTEP在新版本中被移除，使用STEP_SOUND替代
                return Effect.STEP_SOUND;
            case "HAPPY_VILLAGER":
                // HAPPY_VILLAGER在新版本中可能不存在，使用替代效果
                try {
                    return Effect.valueOf("VILLAGER_HAPPY");
                } catch (IllegalArgumentException e) {
                    return Effect.CLICK1; // 安全的默认效果
                }
            default:
                try {
                    return Effect.valueOf(oldEffectName);
                } catch (IllegalArgumentException e) {
                    // 如果找不到效果，返回一个安全的默认效果
                    return Effect.CLICK1;
                }
        }
    }
    
    /**
     * 创建兼容的物品堆
     * 
     * @param materialName 材料名称
     * @param amount 数量
     * @return 物品堆
     */
    public static ItemStack createCompatibleItemStack(String materialName, int amount) {
        Material material = getCompatibleMaterial(materialName);
        return new ItemStack(material, amount);
    }
    
    /**
     * 创建兼容的物品堆（带损伤值）
     * 
     * @param materialName 材料名称
     * @param amount 数量
     * @param damage 损伤值（在1.20中通常被忽略）
     * @return 物品堆
     */
    @SuppressWarnings("deprecation")
    public static ItemStack createCompatibleItemStack(String materialName, int amount, short damage) {
        Material material = getCompatibleMaterial(materialName);
        ItemStack itemStack = new ItemStack(material, amount);
        
        // 在1.20中，大多数损伤值已被移除，但为了兼容性保留此方法
        try {
            itemStack.setDurability(damage);
        } catch (Exception e) {
            // 静默失败，新版本中可能不支持设置损伤值
        }
        
        return itemStack;
    }
    
    /**
     * 检查是否为床方块材料
     * 
     * @param material 材料
     * @return 是否为床方块
     */
    public static boolean isBedMaterial(Material material) {
        if (material == null) {
            return false;
        }
        
        String materialName = material.name();
        return materialName.endsWith("_BED") || 
               materialName.equals("BED") || 
               materialName.equals("BED_BLOCK");
    }
    
    /**
     * 检查是否为羊毛材料
     * 
     * @param material 材料
     * @return 是否为羊毛
     */
    public static boolean isWoolMaterial(Material material) {
        if (material == null) {
            return false;
        }
        
        String materialName = material.name();
        return materialName.endsWith("_WOOL") || materialName.equals("WOOL");
    }
    
    /**
     * 检查是否为染色玻璃材料
     * 
     * @param material 材料
     * @return 是否为染色玻璃
     */
    public static boolean isStainedGlassMaterial(Material material) {
        if (material == null) {
            return false;
        }
        
        String materialName = material.name();
        return materialName.endsWith("_STAINED_GLASS") || 
               materialName.equals("STAINED_GLASS");
    }
    
    /**
     * 获取物品的类型ID（兼容方法）
     * 在1.20中，getTypeId()方法已被移除，使用Material.ordinal()替代
     * 
     * @param itemStack 物品堆
     * @return 类型ID
     */
    @SuppressWarnings("deprecation")
    public static int getCompatibleTypeId(ItemStack itemStack) {
        if (itemStack == null || itemStack.getType() == null) {
            return 0;
        }
        
        try {
            // 尝试使用反射调用getTypeId方法
            return (Integer) itemStack.getClass().getMethod("getTypeId").invoke(itemStack);
        } catch (Exception e) {
            // 使用新方法：Material的序号
            return itemStack.getType().ordinal();
        }
    }
    
    /**
     * 获取库存标题（兼容方法）
     * 在某些版本中，getTitle()方法可能不存在
     * 
     * @param inventory 库存对象
     * @return 标题字符串
     */
    public static String getCompatibleInventoryTitle(org.bukkit.inventory.Inventory inventory) {
        try {
            // 尝试使用反射获取标题
            return (String) inventory.getClass().getMethod("getTitle").invoke(inventory);
        } catch (Exception e) {
            // 如果失败，返回默认标题
            return "Inventory";
        }
    }
}
