package me.ram.bedwarsscoreboardaddon.utils;

import java.util.List;
import java.util.Set;

import org.bukkit.Material;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

public class ItemUtil {

	public static ItemStack createItem(String name) {
		try {
			// 首先尝试直接获取材料
			Material material = Material.getMaterial(name);
			if (material != null) {
				return new ItemStack(material);
			}
			// 如果失败，尝试使用valueOf
			return new ItemStack(Material.valueOf(name));
		} catch (Exception e) {
			// 静默失败，返回AIR
		}
		return new ItemStack(Material.AIR);
	}

	public static ItemStack createItem(String name, int amount) {
		try {
			// 首先尝试直接获取材料
			Material material = Material.getMaterial(name);
			if (material != null) {
				return new ItemStack(material, amount);
			}
			// 如果失败，尝试使用valueOf
			return new ItemStack(Material.valueOf(name), amount);
		} catch (Exception e) {
			// 静默失败，返回AIR
		}
		return new ItemStack(Material.AIR);
	}

	public static ItemStack createItem(String name, int amount, short damage) {
		try {
			// 首先尝试直接获取材料
			Material material = Material.getMaterial(name);
			if (material != null) {
				return new ItemStack(material, amount, damage);
			}
			// 如果失败，尝试使用valueOf
			return new ItemStack(Material.valueOf(name), amount, damage);
		} catch (Exception e) {
			// 静默失败，返回AIR
		}
		return new ItemStack(Material.AIR);
	}

	public static void setItemName(ItemStack item, String name) {
		ItemMeta meta = item.getItemMeta();
		meta.setDisplayName(name);
		item.setItemMeta(meta);
	}

	public static void setItemLore(ItemStack item, List<String> lore) {
		ItemMeta meta = item.getItemMeta();
		meta.setLore(lore);
		item.setItemMeta(meta);
	}

	public static void setItemUnbreak(ItemStack item, boolean unbreak) {
		ItemMeta meta = item.getItemMeta();
		try {
			meta.setUnbreakable(unbreak);
		} catch (Exception e) {
			try {
				// 尝试使用反射调用spigot方法
				Object spigot = meta.getClass().getMethod("spigot").invoke(meta);
				spigot.getClass().getMethod("setUnbreakable", boolean.class).invoke(spigot, unbreak);
			} catch (Exception ex) {
				// 静默失败
			}
		}
		item.setItemMeta(meta);
	}

	public static void addItemFlags(ItemStack item, ItemFlag... flags) {
		ItemMeta meta = item.getItemMeta();
		meta.addItemFlags(flags);
		item.setItemMeta(meta);
	}

	public String getItemName(ItemStack item) {
		ItemMeta meta = item.getItemMeta();
		if (!meta.hasDisplayName()) {
			return item.getType().name();
		}
		return item.getItemMeta().getDisplayName();
	}

	public List<String> getItemLore(ItemStack item) {
		return item.getItemMeta().getLore();
	}

	public Set<ItemFlag> getItemFlags(ItemStack item) {
		return item.getItemMeta().getItemFlags();
	}
}
