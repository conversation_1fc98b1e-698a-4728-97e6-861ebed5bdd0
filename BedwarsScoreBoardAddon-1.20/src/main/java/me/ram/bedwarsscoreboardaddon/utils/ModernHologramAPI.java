package me.ram.bedwarsscoreboardaddon.utils;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import me.ram.bedwarsscoreboardaddon.Main;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 现代化全息图API - 1.20版本
 * 使用ArmorStand实体替代NMS反射，提供更好的兼容性
 * 
 * <AUTHOR>
 * @version 2.13.1-1.20
 */
public class ModernHologramAPI {
    
    private static final Map<String, List<ArmorStand>> holograms = new ConcurrentHashMap<>();
    private static final Map<String, Location> hologramLocations = new ConcurrentHashMap<>();
    private static final double LINE_HEIGHT = 0.25; // 每行之间的高度
    
    /**
     * 创建全息图
     * 
     * @param id 全息图ID
     * @param location 位置
     * @param lines 文本行
     * @param players 可见的玩家列表（null表示所有玩家可见）
     */
    public static void createHologram(String id, Location location, List<String> lines, List<Player> players) {
        // 删除已存在的全息图
        removeHologram(id);
        
        List<ArmorStand> stands = new ArrayList<>();
        hologramLocations.put(id, location.clone());
        
        // 从上到下创建每一行
        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i);
            if (line == null || line.trim().isEmpty()) {
                continue;
            }
            
            // 计算每行的位置（从上到下）
            Location lineLocation = location.clone().add(0, (lines.size() - 1 - i) * LINE_HEIGHT, 0);
            
            // 创建盔甲架
            ArmorStand armorStand = (ArmorStand) location.getWorld().spawnEntity(lineLocation, EntityType.ARMOR_STAND);
            
            // 设置盔甲架属性
            armorStand.setVisible(false);
            armorStand.setGravity(false);
            armorStand.setCanPickupItems(false);
            armorStand.setCustomNameVisible(true);
            armorStand.setCustomName(line);
            armorStand.setMarker(true);
            armorStand.setSmall(true);
            armorStand.setBasePlate(false);
            armorStand.setArms(false);
            
            // 如果指定了玩家列表，设置可见性
            if (players != null) {
                // 对所有其他玩家隐藏
                for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                    if (!players.contains(onlinePlayer)) {
                        onlinePlayer.hideEntity(Main.getInstance(), armorStand);
                    }
                }
            }
            
            stands.add(armorStand);
        }
        
        holograms.put(id, stands);
    }
    
    /**
     * 更新全息图内容
     * 
     * @param id 全息图ID
     * @param lines 新的文本行
     */
    public static void updateHologram(String id, List<String> lines) {
        Location location = hologramLocations.get(id);
        if (location == null) {
            return;
        }
        
        List<Player> visiblePlayers = getVisiblePlayers(id);
        removeHologram(id);
        createHologram(id, location, lines, visiblePlayers);
    }
    
    /**
     * 移除全息图
     * 
     * @param id 全息图ID
     */
    public static void removeHologram(String id) {
        List<ArmorStand> stands = holograms.get(id);
        if (stands != null) {
            for (ArmorStand stand : stands) {
                if (stand != null && !stand.isDead()) {
                    stand.remove();
                }
            }
            holograms.remove(id);
        }
        hologramLocations.remove(id);
    }
    
    /**
     * 移动全息图到新位置
     * 
     * @param id 全息图ID
     * @param newLocation 新位置
     */
    public static void moveHologram(String id, Location newLocation) {
        List<ArmorStand> stands = holograms.get(id);
        if (stands == null) {
            return;
        }
        
        Location oldLocation = hologramLocations.get(id);
        if (oldLocation == null) {
            return;
        }
        
        hologramLocations.put(id, newLocation.clone());
        
        // 计算位置偏移
        double deltaX = newLocation.getX() - oldLocation.getX();
        double deltaY = newLocation.getY() - oldLocation.getY();
        double deltaZ = newLocation.getZ() - oldLocation.getZ();
        
        // 移动每个盔甲架
        for (ArmorStand stand : stands) {
            if (stand != null && !stand.isDead()) {
                Location currentLoc = stand.getLocation();
                stand.teleport(currentLoc.add(deltaX, deltaY, deltaZ));
            }
        }
    }
    
    /**
     * 设置全息图对特定玩家的可见性
     * 
     * @param id 全息图ID
     * @param player 玩家
     * @param visible 是否可见
     */
    public static void setVisibility(String id, Player player, boolean visible) {
        List<ArmorStand> stands = holograms.get(id);
        if (stands == null) {
            return;
        }
        
        for (ArmorStand stand : stands) {
            if (stand != null && !stand.isDead()) {
                if (visible) {
                    player.showEntity(Main.getInstance(), stand);
                } else {
                    player.hideEntity(Main.getInstance(), stand);
                }
            }
        }
    }
    
    /**
     * 检查全息图是否存在
     * 
     * @param id 全息图ID
     * @return 是否存在
     */
    public static boolean exists(String id) {
        return holograms.containsKey(id) && hologramLocations.containsKey(id);
    }
    
    /**
     * 获取全息图的位置
     * 
     * @param id 全息图ID
     * @return 位置，如果不存在则返回null
     */
    public static Location getLocation(String id) {
        return hologramLocations.get(id);
    }
    
    /**
     * 获取所有全息图ID
     * 
     * @return 全息图ID集合
     */
    public static Set<String> getAllHologramIds() {
        return new HashSet<>(holograms.keySet());
    }
    
    /**
     * 清理所有全息图
     */
    public static void cleanup() {
        for (String id : new HashSet<>(holograms.keySet())) {
            removeHologram(id);
        }
    }
    
    /**
     * 获取能看到指定全息图的玩家列表
     * 
     * @param id 全息图ID
     * @return 可见玩家列表
     */
    private static List<Player> getVisiblePlayers(String id) {
        List<ArmorStand> stands = holograms.get(id);
        if (stands == null || stands.isEmpty()) {
            return null;
        }
        
        List<Player> visiblePlayers = new ArrayList<>();
        ArmorStand firstStand = stands.get(0);
        
        if (firstStand != null && !firstStand.isDead()) {
            for (Player player : Bukkit.getOnlinePlayers()) {
                if (player.canSee(firstStand)) {
                    visiblePlayers.add(player);
                }
            }
        }
        
        return visiblePlayers.isEmpty() ? null : visiblePlayers;
    }
    
    /**
     * 定期清理无效的全息图
     */
    public static void startCleanupTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                for (String id : new HashSet<>(holograms.keySet())) {
                    List<ArmorStand> stands = holograms.get(id);
                    if (stands != null) {
                        stands.removeIf(stand -> stand == null || stand.isDead());
                        if (stands.isEmpty()) {
                            holograms.remove(id);
                            hologramLocations.remove(id);
                        }
                    }
                }
            }
        }.runTaskTimer(Main.getInstance(), 20 * 60, 20 * 60); // 每分钟运行一次
    }
}
