package me.ram.bedwarsscoreboardaddon.utils;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

/**
 * ServerJoiner工具类 - 1.20兼容版本
 * 使用反射来避免硬依赖，支持可选插件
 */
public class ServerJoinerUtil {

	/**
	 * 发送玩家到指定服务器组
	 *
	 * @param player 玩家
	 * @param group 服务器组
	 */
	public static void sendServer(Player player, String group) {
		// 检查ServerJoiner插件是否存在
		if (!isServerJoinerEnabled()) {
			return;
		}

		try {
			// 使用反射调用ServerJoiner API
			Class<?> mainClass = Class.forName("com.mcjtf.ServerJoiner.Main");
			Class<?> settingsClass = Class.forName("com.mcjtf.ServerJoiner.Settings");
			Class<?> serverGUIClass = Class.forName("com.mcjtf.ServerJoiner.data.ServerGUI");

			// 获取serverGroup字段
			Object serverGroup = mainClass.getField("serverGroup").get(null);
			if (serverGroup instanceof java.util.Map) {
				@SuppressWarnings("unchecked")
				java.util.Map<String, Object> groupMap = (java.util.Map<String, Object>) serverGroup;

				if (groupMap.containsKey(group)) {
					// 获取guiMap字段
					Object guiMap = mainClass.getField("guiMap").get(null);
					if (guiMap instanceof java.util.Map) {
						@SuppressWarnings("unchecked")
						java.util.Map<String, Object> guiMapCasted = (java.util.Map<String, Object>) guiMap;

						Object gui = guiMapCasted.get(group);
						if (gui != null) {
							// 调用getAutoJoinServer方法
							String server = (String) serverGUIClass.getMethod("getAutoJoinServer").invoke(gui);
							if (server == null) {
								// 获取msg_no_room字段
								String msgNoRoom = (String) settingsClass.getField("msg_no_room").get(null);
								player.sendMessage(msgNoRoom);
								return;
							}

							// 调用send方法
							mainClass.getMethod("send", Player.class, String.class).invoke(null, player, server);
						}
					}
				}
			}
		} catch (Exception e) {
			// 静默失败，避免错误输出
		}
	}

	/**
	 * 检查ServerJoiner插件是否启用
	 *
	 * @return 是否启用
	 */
	public static boolean isServerJoinerEnabled() {
		return Bukkit.getPluginManager().isPluginEnabled("ServerJoiner");
	}
}
