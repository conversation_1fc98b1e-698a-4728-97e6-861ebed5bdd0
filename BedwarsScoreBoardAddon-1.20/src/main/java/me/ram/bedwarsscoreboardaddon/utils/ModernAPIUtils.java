package me.ram.bedwarsscoreboardaddon.utils;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import net.md_5.bungee.api.ChatMessageType;
import net.md_5.bungee.api.chat.TextComponent;

/**
 * 现代化API工具类，用于1.20版本的ActionBar和Title发送
 * 避免使用NMS反射，使用现代Bukkit API
 * 
 * <AUTHOR>
 * @version 2.13.1-1.20
 */
public class ModernAPIUtils {
    
    /**
     * 发送ActionBar消息（使用现代API）
     * 
     * @param player 目标玩家
     * @param message 消息内容
     */
    public static void sendActionBar(Player player, String message) {
        if (player == null || !player.isOnline()) {
            return;
        }
        
        try {
            // 使用现代Spigot API发送ActionBar
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR, new TextComponent(message));
        } catch (Exception e) {
            // 如果现代API失败，尝试使用Paper API
            try {
                // 使用反射调用sendActionBar方法
                player.getClass().getMethod("sendActionBar", String.class).invoke(player, message);
            } catch (Exception ex) {
                // 静默失败，避免控制台错误
            }
        }
    }
    
    /**
     * 发送Title和Subtitle（使用现代API）
     * 
     * @param player 目标玩家
     * @param fadeIn 淡入时间（tick）
     * @param stay 停留时间（tick）
     * @param fadeOut 淡出时间（tick）
     * @param title 主标题
     * @param subtitle 副标题
     */
    public static void sendTitle(Player player, int fadeIn, int stay, int fadeOut, String title, String subtitle) {
        if (player == null || !player.isOnline()) {
            return;
        }
        
        try {
            // 使用现代Bukkit API发送Title
            player.sendTitle(
                title != null ? title : "",
                subtitle != null ? subtitle : "",
                fadeIn,
                stay,
                fadeOut
            );
        } catch (Exception e) {
            // 如果现代API失败，尝试使用反射方法（兼容性）
            sendTitleLegacy(player, fadeIn, stay, fadeOut, title, subtitle);
        }
    }
    
    /**
     * 清除玩家的Title显示
     * 
     * @param player 目标玩家
     */
    public static void clearTitle(Player player) {
        if (player == null || !player.isOnline()) {
            return;
        }
        
        try {
            // 使用现代API清除Title
            player.resetTitle();
        } catch (Exception e) {
            // 如果现代API失败，发送空Title
            try {
                player.sendTitle("", "", 0, 1, 0);
            } catch (Exception ex) {
                // 静默失败
            }
        }
    }
    
    /**
     * 兼容性方法：使用反射发送Title（作为后备方案）
     * 
     * @param player 目标玩家
     * @param fadeIn 淡入时间
     * @param stay 停留时间
     * @param fadeOut 淡出时间
     * @param title 主标题
     * @param subtitle 副标题
     */
    private static void sendTitleLegacy(Player player, int fadeIn, int stay, int fadeOut, String title, String subtitle) {
        try {
            // 获取服务器版本
            String version = Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];
            
            // 获取NMS类
            Class<?> craftPlayerClass = Class.forName("org.bukkit.craftbukkit." + version + ".entity.CraftPlayer");
            Class<?> packetPlayOutTitleClass = Class.forName("net.minecraft.server." + version + ".PacketPlayOutTitle");
            Class<?> iChatBaseComponentClass = Class.forName("net.minecraft.server." + version + ".IChatBaseComponent");
            Class<?> playerConnectionClass = Class.forName("net.minecraft.server." + version + ".PlayerConnection");
            
            // 获取枚举类型
            Object[] enumConstants = packetPlayOutTitleClass.getDeclaredClasses()[0].getEnumConstants();
            Object titleEnum = enumConstants[0]; // TITLE
            Object subtitleEnum = enumConstants[1]; // SUBTITLE
            Object timesEnum = enumConstants[2]; // TIMES
            
            // 获取玩家连接
            Object craftPlayer = craftPlayerClass.cast(player);
            Object handle = craftPlayerClass.getMethod("getHandle").invoke(craftPlayer);
            Object playerConnection = handle.getClass().getField("playerConnection").get(handle);
            
            // 发送时间包
            Object timesPacket = packetPlayOutTitleClass
                .getConstructor(packetPlayOutTitleClass.getDeclaredClasses()[0], iChatBaseComponentClass, int.class, int.class, int.class)
                .newInstance(timesEnum, null, fadeIn, stay, fadeOut);
            playerConnectionClass.getMethod("sendPacket", Class.forName("net.minecraft.server." + version + ".Packet"))
                .invoke(playerConnection, timesPacket);
            
            // 发送标题
            if (title != null && !title.isEmpty()) {
                Object titleComponent = iChatBaseComponentClass.getDeclaredClasses()[0]
                    .getMethod("a", String.class)
                    .invoke(null, "{\"text\":\"" + title + "\"}");
                Object titlePacket = packetPlayOutTitleClass
                    .getConstructor(packetPlayOutTitleClass.getDeclaredClasses()[0], iChatBaseComponentClass)
                    .newInstance(titleEnum, titleComponent);
                playerConnectionClass.getMethod("sendPacket", Class.forName("net.minecraft.server." + version + ".Packet"))
                    .invoke(playerConnection, titlePacket);
            }
            
            // 发送副标题
            if (subtitle != null && !subtitle.isEmpty()) {
                Object subtitleComponent = iChatBaseComponentClass.getDeclaredClasses()[0]
                    .getMethod("a", String.class)
                    .invoke(null, "{\"text\":\"" + subtitle + "\"}");
                Object subtitlePacket = packetPlayOutTitleClass
                    .getConstructor(packetPlayOutTitleClass.getDeclaredClasses()[0], iChatBaseComponentClass)
                    .newInstance(subtitleEnum, subtitleComponent);
                playerConnectionClass.getMethod("sendPacket", Class.forName("net.minecraft.server." + version + ".Packet"))
                    .invoke(playerConnection, subtitlePacket);
            }
            
        } catch (Exception e) {
            // 静默失败，避免控制台错误
        }
    }
    
    /**
     * 检查是否支持现代API
     * 
     * @return 是否支持现代API
     */
    public static boolean isModernAPISupported() {
        try {
            // 检查是否存在现代API方法
            Player.class.getMethod("sendTitle", String.class, String.class, int.class, int.class, int.class);
            return true;
        } catch (NoSuchMethodException e) {
            return false;
        }
    }
    
    /**
     * 获取服务器版本信息
     * 
     * @return 服务器版本字符串
     */
    public static String getServerVersion() {
        return Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];
    }
}
