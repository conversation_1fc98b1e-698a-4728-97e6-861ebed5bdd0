package me.ram.bedwarsscoreboardaddon.utils;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import com.comphenix.protocol.ProtocolLibrary;
import com.comphenix.protocol.ProtocolManager;
import com.comphenix.protocol.events.PacketContainer;

/**
 * NMS兼容性工具类 - 1.20版本
 * 使用ProtocolLib替代直接的NMS反射，提供更好的版本兼容性
 * 
 * <AUTHOR>
 * @version 2.13.1-1.20
 */
public class NMSCompatibility {
    
    private static final ProtocolManager protocolManager = ProtocolLibrary.getProtocolManager();
    private static final String SERVER_VERSION = getServerVersion();
    
    /**
     * 发送数据包给玩家（使用ProtocolLib）
     * 
     * @param player 目标玩家
     * @param packet 数据包对象
     */
    public static void sendPacket(Player player, Object packet) {
        try {
            if (packet instanceof PacketContainer) {
                // 如果是ProtocolLib的PacketContainer，直接发送
                protocolManager.sendServerPacket(player, (PacketContainer) packet);
            } else {
                // 尝试使用反射发送原生NMS包（作为后备方案）
                sendPacketLegacy(player, packet);
            }
        } catch (Exception e) {
            // 静默失败，避免控制台错误
        }
    }
    
    /**
     * 传统方式发送数据包（后备方案）
     * 
     * @param player 目标玩家
     * @param packet 数据包对象
     */
    private static void sendPacketLegacy(Player player, Object packet) {
        try {
            // 获取玩家的NMS对象
            Object handle = player.getClass().getMethod("getHandle").invoke(player);
            
            // 1.20中playerConnection字段名可能已改变，尝试多个可能的名称
            Object playerConnection = null;
            try {
                // 尝试新的字段名
                playerConnection = handle.getClass().getField("connection").get(handle);
            } catch (NoSuchFieldException e1) {
                try {
                    // 尝试旧的字段名
                    playerConnection = handle.getClass().getField("playerConnection").get(handle);
                } catch (NoSuchFieldException e2) {
                    try {
                        // 尝试其他可能的字段名
                        playerConnection = handle.getClass().getField("b").get(handle);
                    } catch (NoSuchFieldException e3) {
                        return; // 无法找到连接字段
                    }
                }
            }
            
            // 发送数据包
            if (playerConnection != null) {
                // 尝试不同的方法名
                try {
                    playerConnection.getClass().getMethod("sendPacket", packet.getClass().getSuperclass()).invoke(playerConnection, packet);
                } catch (Exception e1) {
                    try {
                        playerConnection.getClass().getMethod("send", packet.getClass().getSuperclass()).invoke(playerConnection, packet);
                    } catch (Exception e2) {
                        // 静默失败
                    }
                }
            }
        } catch (Exception e) {
            // 静默失败
        }
    }
    
    /**
     * 获取NMS类（兼容性方法）
     * 
     * @param name 类名
     * @return NMS类，如果找不到则返回null
     */
    public static Class<?> getNMSClass(String name) {
        try {
            // 1.20使用新的包结构
            return Class.forName("net.minecraft.server.level." + name);
        } catch (ClassNotFoundException e1) {
            try {
                // 尝试旧的包结构
                return Class.forName("net.minecraft.server." + SERVER_VERSION + "." + name);
            } catch (ClassNotFoundException e2) {
                try {
                    // 尝试其他可能的包
                    return Class.forName("net.minecraft.world.entity." + name);
                } catch (ClassNotFoundException e3) {
                    try {
                        return Class.forName("net.minecraft.network.protocol.game." + name);
                    } catch (ClassNotFoundException e4) {
                        return null;
                    }
                }
            }
        }
    }
    
    /**
     * 获取CraftBukkit类
     * 
     * @param name 类名
     * @return CraftBukkit类，如果找不到则返回null
     */
    public static Class<?> getCraftBukkitClass(String name) {
        try {
            return Class.forName("org.bukkit.craftbukkit." + SERVER_VERSION + "." + name);
        } catch (ClassNotFoundException e) {
            return null;
        }
    }
    
    /**
     * 获取服务器版本
     * 
     * @return 服务器版本字符串
     */
    public static String getServerVersion() {
        try {
            return Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];
        } catch (Exception e) {
            return "v1_20_R1"; // 默认版本
        }
    }
    
    /**
     * 检查是否为1.20或更高版本
     * 
     * @return 是否为现代版本
     */
    public static boolean isModernVersion() {
        String version = getServerVersion();
        return version.contains("1_20") || version.contains("1_21") || 
               version.compareTo("v1_20_R1") >= 0;
    }
    
    /**
     * 获取实体ID（兼容性方法）
     * 
     * @param entity 实体对象
     * @return 实体ID
     */
    public static int getEntityId(Object entity) {
        try {
            // 尝试现代方法
            return (Integer) entity.getClass().getMethod("getId").invoke(entity);
        } catch (Exception e1) {
            try {
                // 尝试旧方法
                return (Integer) entity.getClass().getMethod("getEntityId").invoke(entity);
            } catch (Exception e2) {
                try {
                    // 尝试字段访问
                    return entity.getClass().getField("id").getInt(entity);
                } catch (Exception e3) {
                    return -1; // 失败时返回无效ID
                }
            }
        }
    }
    
    /**
     * 创建数据观察器（兼容性方法）
     * 
     * @param entity 实体对象
     * @return 数据观察器对象
     */
    public static Object createDataWatcher(Object entity) {
        try {
            Class<?> dataWatcherClass = getNMSClass("DataWatcher");
            if (dataWatcherClass == null) {
                dataWatcherClass = getNMSClass("SynchedEntityData");
            }
            
            if (dataWatcherClass != null) {
                return dataWatcherClass.getConstructor(getNMSClass("Entity")).newInstance(entity);
            }
        } catch (Exception e) {
            // 静默失败
        }
        return null;
    }
    
    /**
     * 检查ProtocolLib是否可用
     * 
     * @return ProtocolLib是否可用
     */
    public static boolean isProtocolLibAvailable() {
        try {
            return protocolManager != null && Bukkit.getPluginManager().isPluginEnabled("ProtocolLib");
        } catch (Exception e) {
            return false;
        }
    }
}
