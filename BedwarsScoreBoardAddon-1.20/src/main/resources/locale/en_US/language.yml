#Config version (Don't edit!)
version: 4

button:
  list_teleport: "&f[&eTeleport&f]"
  list_remove: "&f[&cRemove&f]"
show_text:
  list_teleport: "&eClick to teleport!"
  list_remove: "&cClick to remove!"
commands:
  help:
  - " &e/bwsba  &7-&f  Plugin info"
  - " &e/bwsba help  &7-&f  Get help"
  - " &e/bwsba shop  &7-&f  Shop settings"
  - " &e/bwsba spawner  &7-&f  Team spawner settings"
  - " &e/bwsba edit <Game>  &7-&f  Edit game"
  - " &e/bwsba reload  &7-&f  Reload configuration"
  - " &e/bwsba upcheck  &7-&f  Update check"
  message:
    prefix: "&b&lBWSBA &f>> "
    no_permission: "&cNo permission to use this command!"
    reloaded: "&aReload completed!"
    not_player: "&cYou are not a player!"
    set_item_shop: "&aAdded!"
    set_shop_error: "&aCitizens &funloaded!"
    failed_set_shop: "&c&cFail to add!"
    shop_list_error: "&cGame does not exist or shop is not set!"
    remove_shop: "&aRemoved!"
    failed_remove_shop: "&cShop do not exist!"
    shop_list: "&fShop list:"
    add_spawner: "&aAdded!"
    remove_spawner: "&aRemoved!"
    spawner_list: "&fTeam spawner list:"
    spawner_list_error: "&cGame does not exist or spawner is not set!"
    failed_remove_spawner: "&cTeam spawner do not exist!"
    edit_game_error: "&cGame does not exist!"
    help:
      shop_list: "&e/bwsba shop list <Game> &7-&f Shop list"
      remove_shop: "&e/bwsba shop remove <ID> &7-&f Remove a shop"
      set_item_shop: "&e/bwsba shop set item <Game> &7-&f Add a item shop"
      set_team_shop: "&e/bwsba shop set team <Game> &7-&f Add a team shop"
      spawner_list: "&e/bwsba spawner list <Game> &7-&f Team spawner list"
      remove_spawner: "&e/bwsba spawner remove <ID> &7-&f Remove a team spawner"
      add_spawner: "&e/bwsba spawner add <Game> <Team> &7-&f Add a team spawner"
      edit_game: "&e/bwsba edit <Game> &7-&f Edit game"
      unknown: "Unknown command! Use &e/bwsba help &ffor help."
inventory:
  edit_game: "EditGame"
item:
  edit_game:
    name:
      menu_item: "&eGame Edit Menu"
      align_angle_item: "&eAlign Angle"
      align_location_item: "&eAlign Location"
      teleport_tool: "&eTeleport Tool"
      back: "&fBack"
      set_lobby: "&fSet lobby"
      set_mix_players: "&fSet mix players"
      edit_team: "&fEdit team"
      set_bed: "&fSet team bed"
      set_spawn: "&fSet team spawn"
      set_item_shop: "&fSet item shop"
      set_team_shop: "&fSet team shop"
      set_spawner: "&fSet resource spawner"
      set_team_spawner: "&fTeam resource spawner"
      set_game_spawner: "&fGame resource spawner"
      set_region_loc1: "&fSet region location A"
      set_region_loc2: "&fSet region location B"
      save_game: "&bSave Game"
      start_game: "&aStart Game"
      stop_game: "&cStop Game"
    lore:
      set: "&eClick to set!"
      add: "&eClick to add!"
      remove: "&cClick to remove!"
      complete: "&aComplete!"
      mix_players: "&eMin players &c{players}"
      max_players: "&eMax players &c{players}"
      browse: "&eClick Browse!"
anvil:
  edit_game:
    set_mix_players: "Enter min players"
    set_team_name: "Enter team name"
    set_team_max_players: "Enter team max players"
holographic:
  edit_game:
    loc1: "&aLocation A"
    loc2: "&aLocation B"
    lobby: "&aWaiting Lobby"
    bed: "{team} &fBed"
    spawn: "{team} &fSpawn"
    spawner: "&a{resource} Spawner"
    team_spawner: "{team}&f Team spawner"
  shop:
    item: "&fID: &a{id} &eItem Shop"
    team: "&fID: &a{id} &eTeam Shop"
