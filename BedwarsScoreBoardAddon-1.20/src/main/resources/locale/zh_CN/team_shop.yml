#配置版本 (请勿修改)
version: 6

#是否启用
enabled: true

#商店
upgrade_shop:

  #商店标题
  title: "升级与陷阱"

  #装饰栏
  frame:
  - "&8\u2b06 &7升级"
  - "&8\u2b07 &7陷阱"

  #陷阱
  trap:

    #物品名称
    item: LEATHER

    #升级名称
    name: "&e购买一个陷阱"

    lore:
    - "&7已购买的陷阱将加入右边的队列中."
    - ""
    - "&e点击浏览！"

#陷阱商店
trap_shop:
  #标题
  title: "选择陷阱"

  #返回
  back:
  - "&a返回"
  - "&7升级与陷阱"

#提示
message:

  #升级
  upgrade: "&7{player} &a购买了 &6{upgrade} {level}"

  #资源不足
  no_resource: "&c你没有足够的资源购买!"

#状态
state:

  no_resource: "&c你没有足够的资源！"

  lock: "&e点击购买！"

  unlock: "&a已解锁！"

#触发陷阱冷却时间 (秒)
trap_cooldown: 60

#陷阱
trap:

  #陷阱列表
  trap_list:
    trap_1:
      lock:
      - "&c陷阱 #1: 没有陷阱！"
      - "&7第一个敌人进入己方基地后"
      - "&7会触发该陷阱！"
      - ""
      - "&7购买的陷阱会在此排列,具体"
      - "&7费用取决于已排列的陷阱数量。"
      - ""
      - "&7下一个陷阱:&b{cost} 钻石"
      unlock:
      - "&a陷阱 #1: {trap}！"
      - ""
      - "&7第一个敌人进入己方基地后"
      - "&7会触发该陷阱！"
      - ""
      - "&7购买者:{buyer}"
    trap_2:
      lock:
      - "&c陷阱 #2: 没有陷阱！"
      - "&7第二个敌人进入己方基地后"
      - "&7会触发该陷阱！"
      - ""
      - "&7购买的陷阱会在此排列,具体"
      - "&7费用取决于已排列的陷阱数量。"
      - ""
      - "&7下一个陷阱:&b{cost} 钻石"
      unlock:
      - "&a陷阱 #2: {trap}！"
      - ""
      - "&7第二个敌人进入己方基地后"
      - "&7会触发该陷阱！"
      - ""
      - "&7购买者:{buyer}"
    trap_3:
      lock:
      - "&c陷阱 #3: 没有陷阱！"
      - "&7第三个敌人进入己方基地后"
      - "&7会触发该陷阱！"
      - ""
      - "&7购买的陷阱会在此排列,具体"
      - "&7费用取决于已排列的陷阱数量。"
      - ""
      - "&7下一个陷阱:&b{cost} 钻石"
      unlock:
      - "&a陷阱 #3: {trap}！"
      - ""
      - "&7第三个敌人进入己方基地后"
      - "&7会触发该陷阱！"
      - ""
      - "&7购买者:{buyer}"

  #需要的资源
  #格式:
  #  level: 物品,数量
  #
  #经验模式格式:
  #  level: XP,数量
  cost:
    level_1: DIAMOND,1
    level_2: DIAMOND,2
    level_3: DIAMOND,4

#升级
upgrade:

  #疯狂矿工
  fast_dig:

    #是否启用
    enabled: true

    #物品名称
    item: GOLD_PICKAXE

    #升级名称
    name: "疯狂矿工"

    #等级一
    level_1:

      #需要的资源
      # cost: 物品,数量"
      #经验模式
      # cost: XP,数量"
      cost: DIAMOND,4
      lore:
      - "&7己方所有成员获得永久急迫效果。"
      - ""
      - "&71阶:急迫I&7,&b4 钻石"
      - "&72阶:急迫II&7,&b6 钻石"
      - ""
      - "{state}"

    #等级二
    level_2:
      cost: DIAMOND,6
      lore:
      - "&7己方所有成员获得永久急迫效果。"
      - ""
      - "&a1阶:急迫I&7,&b4 钻石"
      - "&72阶:急迫II&7,&b6 钻石"
      - ""
      - "{state}"

    #满级
    level_full:
      lore:
      - "&7己方所有成员获得永久急迫效果。"
      - ""
      - "&a1阶:急迫I&7,&b4 钻石"
      - "&a2阶:急迫II&7,&b6 钻石"
      - ""
      - "{state}"

  #锋利附魔
  sword_sharpness:
    enabled: true
    item: IRON_SWORD
    name: "锋利附魔"
    level_1:
      cost: DIAMOND,8
      lore:
      - "&7己方所有成员的剑和斧将永久获得锋利附魔！"
      - ""
      - "&71阶:锋利I&7,&b8 钻石"
      - "&72阶:锋利II&7,&b12 钻石"
      - ""
      - "{state}"
    level_2:
      cost: DIAMOND,12
      lore:
      - "&7己方所有成员的剑和斧将永久获得锋利附魔！"
      - ""
      - "&a1阶:锋利I&7,&b8 钻石"
      - "&72阶:锋利II&7,&b12 钻石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7己方所有成员的剑和斧将永久获得锋利附魔！"
      - ""
      - "&a1阶:锋利I&7,&b8 钻石"
      - "&a2阶:锋利II&7,&b12 钻石"
      - ""
      - "{state}"

  #护甲强化
  armor_protection:
    enabled: true
    item: IRON_CHESTPLATE
    name: "护甲强化"
    level_1:
      cost: DIAMOND,5
      lore:
      - "&7己方所有成员的盔甲将获得永久保护附魔！"
      - ""
      - "&71阶:保护I&7,&b5 钻石"
      - "&72阶:保护II&7,&b10 钻石"
      - "&73阶:保护III&7,&b20 钻石"
      - "&74阶:保护IV&7,&b30 钻石"
      - ""
      - "{state}"
    level_2:
      cost: DIAMOND,10
      lore:
      - "&7己方所有成员的盔甲将获得永久保护附魔！"
      - ""
      - "&a1阶:保护I&7,&b5 钻石"
      - "&72阶:保护II&7,&b10 钻石"
      - "&73阶:保护III&7,&b20 钻石"
      - "&74阶:保护IV&7,&b30 钻石"
      - ""
      - "{state}"
    level_3:
      cost: DIAMOND,20
      lore:
      - "&7己方所有成员的盔甲将获得永久保护附魔！"
      - ""
      - "&a1阶:保护I&7,&b5 钻石"
      - "&a2阶:保护II&7,&b10 钻石"
      - "&73阶:保护III&7,&b20 钻石"
      - "&74阶:保护IV&7,&b30 钻石"
      - ""
      - "{state}"
    level_4:
      cost: DIAMOND,30
      lore:
      - "&7己方所有成员的盔甲将获得永久保护附魔！"
      - ""
      - "&a1阶:保护I&7,&b5 钻石"
      - "&a2阶:保护II&7,&b10 钻石"
      - "&a3阶:保护III&7,&b20 钻石"
      - "&74阶:保护IV&7,&b30 钻石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7己方所有成员的盔甲将获得永久保护附魔！"
      - ""
      - "&a1阶:保护I&7,&b5 钻石"
      - "&a2阶:保护II&7,&b10 钻石"
      - "&a3阶:保护III&7,&b20 钻石"
      - "&a4阶:保护IV&7,&b30 钻石"
      - ""
      - "{state}"

  #治愈池
  heal:
    enabled: true
    item: BEACON
    name: "治愈池"
    trigger_range: 10
    level_1:
      cost: DIAMOND,3
      lore:
      - "&7基地附近的队伍成员获得生命恢复效果。"
      - ""
      - "&7花费:&b3 钻石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7基地附近的队伍成员获得生命恢复效果。"
      - ""
      - "&7花费:&b3 钻石"
      - ""
      - "{state}"

  #铁锻炉
  iron_forge:
    enabled: true
    item: FURNACE
    name: "铁锻炉"
    level_0:
      #资源生成速度 (每隔多少tick生成一个资源)
      #1 秒 = 20 tick
      #resources:
      #- 物品名称,tick,扩散范围
      resources:
      - IRON_INGOT,16,0.0
      - GOLD_INGOT,80,0.0
    level_1:
      cost: DIAMOND,4
      lore:
      - "&7提升自己岛上生成资源的效率。"
      - ""
      - "&71阶:+50%资源,&b4 钻石"
      - "&72阶:+100%资源,&b8 钻石"
      - "&73阶:生成绿宝石,&b12 钻石"
      - "&74阶:+200%资源,&b16钻石"
      - ""
      - "{state}"
      resources:
      - IRON_INGOT,11,0.0
      - GOLD_INGOT,53,0.0
    level_2:
      cost: DIAMOND,8
      lore:
      - "&7提升自己岛上生成资源的效率。"
      - ""
      - "&a1阶:+50%资源,&b4 钻石"
      - "&72阶:+100%资源,&b8 钻石"
      - "&73阶:生成绿宝石,&b12 钻石"
      - "&74阶:+200%资源,&b16钻石"
      - ""
      - "{state}"
      resources:
      - IRON_INGOT,8,0.0
      - GOLD_INGOT,40,0.0
    level_3:
      cost: DIAMOND,12
      lore:
      - "&7提升自己岛上生成资源的效率。"
      - ""
      - "&a1阶:+50%资源,&b4 钻石"
      - "&a2阶:+100%资源,&b8 钻石"
      - "&73阶:生成绿宝石,&b12 钻石"
      - "&74阶:+200%资源,&b16钻石"
      - ""
      - "{state}"
      resources:
      - IRON_INGOT,8,0.0
      - GOLD_INGOT,40,0.0
      - EMERALD,1000,0.0
    level_4:
      cost: DIAMOND,16
      lore:
      - "&7提升自己岛上生成资源的效率。"
      - ""
      - "&a1阶:+50%资源,&b4 钻石"
      - "&a2阶:+100%资源,&b8 钻石"
      - "&a3阶:生成绿宝石,&b12 钻石"
      - "&74阶:+200%资源,&b16钻石"
      - ""
      - "{state}"
      resources:
      - IRON_INGOT,5,0.0
      - GOLD_INGOT,27,0.0
      - EMERALD,1000,0.0
    level_full:
      lore:
      - "&7提升自己岛上生成资源的效率。"
      - ""
      - "&a1阶:+50%资源,&b4 钻石"
      - "&a2阶:+100%资源,&b8 钻石"
      - "&a3阶:生成绿宝石,&b12 钻石"
      - "&a4阶:+200%资源,&b16钻石"
      - ""
      - "{state}"

  #这是个陷阱
  trap:
    enabled: true
    item: TRIPWIRE_HOOK
    name: "这是个陷阱"

    #触发范围
    trigger_range: 5

    #触发后
    trigger:

      #大标题
      title: "&c触发陷阱！"

      #小标题
      subtitle: "&7敌人已落入你的陷阱"

      #提示
      message: ""
    level_1:
      lore:
      - "&7敌人接近你队伍基地时,造成失明与缓慢效果,持续5秒。"
      - ""
      - "&7花费:&b{cost} 钻石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7敌人接近你队伍基地时,造成失明与缓慢效果,持续5秒。"
      - ""
      - "&7花费:&b{cost} 钻石"
      - ""
      - "{state}"

  #反击陷阱
  counter_offensive_trap:
    enabled: true
    item: FEATHER
    name: "反击陷阱"

    #触发范围
    trigger_range: 5

    #效果范围
    effect_range: 10

    level_1:
      lore:
      - "&7敌人接近你队伍基地时,基地附近的"
      - "&7队伍成员获得速度I与跳跃II效果,"
      - "&7持续10秒。"
      - ""
      - "&7花费:&b{cost} 钻石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7敌人接近你队伍基地时,基地附近的"
      - "&7队伍成员获得速度I与跳跃II效果,"
      - "&7持续10秒。"
      - ""
      - "&7花费:&b{cost} 钻石"
      - ""
      - "{state}"

  #报警陷阱
  alarm_trap:
    enabled: true
    item: REDSTONE_TORCH_ON
    name: "报警陷阱"

    #触发范围
    trigger_range: 5

    #触发后
    trigger:

      #大标题
      title: "&c触发陷阱！"

      #小标题
      subtitle: "&f{player}&7({team_color}{team}&7) 已落入你的陷阱"

      #提示
      message: ""
    level_1:
      lore:
      - "&7显示隐身的玩家"
      - "&7及其名称与队伍名。"
      - ""
      - "&7花费:&b{cost} 钻石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7显示隐身的玩家"
      - "&7及其名称与队伍名。"
      - ""
      - "&7花费:&b{cost} 钻石"
      - ""
      - "{state}"

  #防御系统
  defense:
    enabled: true
    item: IRON_PICKAXE
    name: "防御系统"
    trigger_range: 5
    level_1:
      lore:
      - "&7敌人接近你队伍基地时,造成挖掘疲劳效果"
      - ""
      - "&7花费:&b{cost} 钻石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7敌人接近你队伍基地时,造成挖掘疲劳效果"
      - ""
      - "&7花费:&b{cost} 钻石"
      - ""
      - "{state}"
