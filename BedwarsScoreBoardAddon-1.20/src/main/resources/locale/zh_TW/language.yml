#配置版本 (請勿修改)
version: 4

button:
  list_teleport: "&f[&e傳送&f]"
  list_remove: "&f[&c刪除&f]"
show_text:
  list_teleport: "&e點擊傳送！"
  list_remove: "&c點擊刪除！"
commands:
  help:
  - " &e/bwsba  &7-&f  顯示插件信息"
  - " &e/bwsba help  &7-&f  顯示幫助菜單"
  - " &e/bwsba shop  &7-&f  商店設置"
  - " &e/bwsba spawner  &7-&f  資源點設置"
  - " &e/bwsba edit <遊戲>  &7-&f  編輯遊戲"
  - " &e/bwsba reload  &7-&f  重載配置文件"
  - " &e/bwsba upcheck  &7-&f  檢測版本更新"
  message:
    prefix: "&b&lBWSBA &f>> "
    no_permission: "&c你沒有使用該命令的權限！"
    reloaded: "&a配置文件重載完成！"
    not_player: "&c你不是一個玩家！"
    set_item_shop: "&a商店設置成功！"
    set_shop_error: "&f缺少必要前置: &aCitizens"
    failed_set_shop: "&c商店設置失敗！"
    shop_list_error: "&c遊戲不存在或未設置商店！"
    remove_shop: "&a商店移除成功！"
    failed_remove_shop: "&c商店不存在！"
    shop_list: "&f已設置商店列表:"
    add_spawner: "&a資源點添加成功！"
    remove_spawner: "&a資源點移除成功！"
    spawner_list: "&f隊伍資源點列表:"
    spawner_list_error: "&c遊戲不存在或未設置資源點！"
    failed_remove_spawner: "&c資源點不存在！"
    edit_game_error: "&c遊戲不存在！"
    help:
      shop_list: "&e/bwsba shop list <遊戲> &7-&f 已設置商店列表"
      remove_shop: "&e/bwsba shop remove <ID> &7-&f 移除一個商店"
      set_item_shop: "&e/bwsba shop set item <遊戲> &7-&f 設置一個道具商店"
      set_team_shop: "&e/bwsba shop set team <遊戲> &7-&f 設置一個隊伍商店"
      spawner_list: "&e/bwsba spawner list <遊戲> &7-&f 隊伍資源點列表"
      remove_spawner: "&e/bwsba spawner remove <ID> &7-&f 移除隊伍資源點"
      add_spawner: "&e/bwsba spawner add <遊戲> <隊伍> &7-&f 添加隊伍資源點"
      edit_game: "&e/bwsba edit <遊戲> &7-&f 編輯遊戲"
      unknown: "未知指令！ 使用 &e/bwsba help &f顯示幫助菜單"
inventory:
  edit_game: "編輯遊戲"
item:
  edit_game:
    name:
      menu_item: "&e遊戲編輯菜單"
      align_angle_item: "&e快速對齊視角"
      align_location_item: "&e快速對齊位置"
      teleport_tool: "&e傳送工具"
      back: "&f返回"
      set_lobby: "&f設置大廳"
      set_mix_players: "&f設置最少玩家人數"
      edit_team: "&f編輯隊伍"
      set_bed: "&f設置隊伍床"
      set_spawn: "&f設置隊伍出生點"
      set_item_shop: "&f設置物品商店"
      set_team_shop: "&f設置隊伍商店"
      set_spawner: "&f設置資源生成點"
      set_team_spawner: "&f隊伍資源生成點"
      set_game_spawner: "&f遊戲資源生成點"
      set_region_loc1: "&f設置遊戲區域A"
      set_region_loc2: "&f設置遊戲區域B"
      save_game: "&b保存遊戲"
      start_game: "&a啟用遊戲"
      stop_game: "&c停止遊戲"
    lore:
      set: "&e點擊設置！"
      add: "&e點擊添加！"
      remove: "&c點擊移除！"
      complete: "&a已設置！"
      mix_players: "&e已設置 &c{players} &e人"
      max_players: "&e最大人數 &c{players} &e人"
      browse: "&e點擊瀏覽！"
anvil:
  edit_game:
    set_mix_players: "輸入最小玩家人數"
    set_team_name: "輸入隊伍名稱"
    set_team_max_players: "輸入隊伍最大玩家數"
holographic:
  edit_game:
    loc1: "&a坐標A"
    loc2: "&a坐標B"
    lobby: "&a等待大廳"
    bed: "{team}&f床"
    spawn: "{team}&f出生點"
    spawner: "&a{resource}資源點"
    team_spawner: "{team}&f隊伍資源點"
  shop:
    item: "&fID: &a{id} &e物品商店"
    team: "&fID: &a{id} &e隊伍商店"
