---
default:
  pages: "Page $current$ of $max$"
  currently: Currently
errors:
  argumentslength: "Number of arguments does not match the correct amount!"
  holodependencynotfound: "Couldn't find $dependency$ for Hologram-Statistic"
  packagenotfound: "Couldn't fetch $package$ package!"
  classnotfound: "Couldn't fetch $package$ class $class$!"
  gameexists: "A game with this name already exists!"
  gamenotfound: "اللعبة '$game$' لا يمكن ايجادها!"
  nofreegames: "لا الألعاب متوفرة الان."
  gamenotfoundsimple: "لا يمكن إيجاد اللعبة!"
  playeramount: "الحد الأقصى للاعبين لا يمكن ان يكون تحت 1 او اعلى من 24!"
  teamcolornotallowed: "The given Team color isn't a allowed color"
  teamnamelength: "اسم الفريق يجب ان يكون بين حرفين و 20 حرف!"
  teamnotfound: "لم يتم العثور على فريق!"
  notingame: "أنت الآن ليس في لعبة!"
  bedtargeting: "You have to target or stand on a block of the configured 'game-block' type!"
  regionargument: "Your location argument has to be 'loc1' or 'loc2'!"
  spawnerargument: "The resource parameter has to be a valid configured resource!"
  blockdownnotfound: "The block you're standing on was not found!"
  gamenotrunning: "اللعبة لا تعمل!"
  bungeenoserver: "Bungeecord Servers wasn't set properly! Talk to the server administrator!"
  cantstartagain: "Game is running! You can't start a running game again!"
  startoutofwaiting: "Game have to be started out of the waiting mode!"
  cantjoingame: "لا يمكنك الانضمام إلى لعبة قيد التشغيل أو تم إيقافها!"
  lobbyongameworld: "Lobby can't be on the game world!"
  gamenotloaded: "Couldn't start up the game!"
  gameloaderror: "Loading game '$game$' throws an error!"
  regionnotfound: "Region file does not exists!"
  savesign: "Couldn't create a new sign config file!"
  nogames: "لم يتم العثور على اي ألعاب!"
  notenoughress: "You didn't have enough ressource to buy this item!"
  teamnameinuse: "اسم الفريق مسبقاً قيد الاستخدام!"
  minplayersmustnumber: "Min players must be a number!"
  toolongregionname: "الحد الأقصى لطول اسم المنطقة 15 حرف!"
  notwhilegamerunning: "لا يمكن القيام بذلك أثناء تشغيل اللعبة!"
  notwhileingame: "Cannot do that while you're in a running game!"
  timeincorrect: "Time has to be a number (0 ... 23000), 'day' or 'night'!"
  minplayersnumeric: "The min-players parameter must be numeric!"
  notinair: "أنت لست في الهواء!"
  teamfull: "الفريق كامل، يرجى إعادة فتح قائمة اختيار الفرق!"
  novalidmaterial: "Given block type (material) isn't correct!"
  wrongvalueonoff: "Wrong Parameter! Use true,on,1 to turn on - Use false,off,0 to turn off!"
  playernotfound: "Given player was not found or isn't online!"
  notingameforkick: "يجب أن تكون في اللعبة لطرد لاعب!"
  playernotingame: "اللاعب ليس في هذه اللعبة!"
  mustbeinlobbyworld: "يجب أن تكون في عالم بهو اللعبة"
  addteamjoincancel: "إضافة للانضمام إلى فريق ألغى!"
  entitynotcompatible: "This entity is not compatible with team join!"
success:
  gameadded: "اللعبة '$game$' تمت اضافتها بنجاح!"
  teamadded: "الفريق '$team$' تم اضفاته بنجاح!"
  joined: "You successfully joined the game!"
  left: "You successfully left the game!"
  saved: "تم حفظ اللعبة بنجاح!"
  bedset: "You sucessfully set the respawn block of team $team$!"
  regionset: "Game region location $location$ for Game $game$ was set successfully!"
  spawnset: "Spawn location for Team $team$ was set successfully!"
  spawnerset: "Ressource spawn location for $name$ was set successfully!"
  stopped: "Game successfully stopped!"
  lobbyset: "Lobby was set successfully!"
  gameloaded: "Game '$game$' successfully loaded!"
  reloadconfig: "Reload successful!"
  teamremoved: "تمت إزالة الفريق بنجاح!"
  gameremoved: "تمت إزالة اللعبة بنجاح!"
  spawnercleared: "وقد أزيلت جميع منتجات الموارد!"
  gamerun: "You started the game, players can now join!"
  timeset: "Game time was successfully set!"
  regionnameset: "Region name was set successfully!"
  minplayersset: "Min players was set successfully!"
  mainlobbyset: "Mainlobby was set successfully!"
  gametimeset: "Game time was set successfully!"
  materialset: "The respawn block type (material) was set succesfully!"
  builderset: "The builder for the map was set successfully and will display in title!"
  autobalanceseton: "Autobalance was successfully turned on!"
  autobalancesetoff: "Autobalance was successfully turned &coff&a!"
  selectteamjoinentity: "Now do a right click on the entity you want to use as team join!"
  teamjoinadded: "Entity was successfully marked as team selection for team $team$"
  holoremoved: "Hologram-Statistic successfully removed!"
gamecheck:
  LOC_NOT_SET_ERROR: "Locations for the region were not set properly!"
  TEAM_SIZE_LOW_ERROR: "You have to set more teams!"
  NO_RES_SPAWNER_ERROR: "You didn't set any resource spawner!"
  NO_LOBBY_SET: "You didn't set a lobby!"
  TEAMS_WITHOUT_SPAWNS: "There are team(s) without a spawn location!"
  NO_ITEMSHOP_CATEGORIES: "No itemshop categories found!"
  NO_MAIN_LOBBY_SET: "You didn't set a main lobby even though you did set 'tomainlobby' to true"
  TEAM_NO_WRONG_BED: "One or more teams have no bed set!"
  TEAM_NO_WRONG_TARGET: "One or more teams have no respawn block set!"
ingame:
  team: "Team"
  teams: "Teams"
  all: "All"
  record: "&e$record$&a is the record on this map!"
  record-with-holders: '&aThe record on this map is &e$record$&a and is held by: $holders$'
  newrecord: '&aTeam $team$&a set a new record: &6$record$'
  record-nobeddestroy: "&cRecord won't be saved, because no bed was destroyed!"
  teamwon: "Congratulations! Team $team$ won!"
  draw: "The game ends with a draw!"
  serverrestart: "Server restart in $sec$ second(s)!"
  gamestarting: "Game starting ..."
  gamestarted: "Game '$game$' has just started!"
  backtolobby: "Back to lobby in $sec$ second(s)!"
  spectator: "Spectator"
  spectate: "&aSpectate"
  teamchest: "Team chest"
  noturteamchest: "This chest isn't a chest of your team!"
  protectionleft: "Invulnerable for &c$length$&f second(s)!"
  protectionend: "You're now &cvulnerable&f again!"
  team-dead: "Team $team$ was destroyed!"
  no-friendlybreak: "&cCan't break block under team-member!"
  teamchestdestroy: "&cOne of your teamchest(s) has been destroyed!"
  title:
    map-builder: "Built by $builder$"
    win-title: "&6Congratulations!"
    win-subtitle: "$team$&6 won in &e$time$"
  shop:
    name: "Itemshop"
    newshop: "Use new shop"
    oldshop: "Use old shop"
    fullstackpershift: "Multiply stacks per shift click"
    onestackpershift: "One stack per shift click"
  player:
    left: "Player $player$ has left the game!"
    died: "$player$ died!"
    killed: "$killer$ قتل $player$!"
    kicked: "الاعب $player$ طرد!"
    waskicked: "لقد طردت من اللعبة!"
  blocks:
    ownbeddestroy: "You can't destroy your own bed!"
    beddestroyed: "$player$ destroyed bed of team $team$!"
  specials:
    rescue-platform:
      left: "There are &c$time$&f second(s) left until you can use the next rescue platform!"
    arrow-blocker:
      start: "You are protected from being hit by arrows for &c$time$&f second(s)."
      end: "حماية الأسهم انتهت"
      left: "There are &c$time$&f second(s) left until you can use the next arrowblocker!"
    trap:
      trapped: "&eSomeone went into a &ctrap&e of your team!"
    protection-wall:
      left: "There are &c$time$&f second(s) left until you can use the next protection wall!"
      not-usable-here: "لا يمكنك استخدام جدار الحماية هنا!"
    warp-powder:
      cancelled: "&cانتقالك قد الغي!"
      start: "سوف يتم نقلك خلال &c$time$&f ثانية, لا تتحرك"
      cancel: "&4Cancel teleport"
      multiuse: "&cYou started a teleportation already!"
    tntsheep:
      no-target-found: "No target player was found!"
    tracker:
      no-target-found: "No target player was found!"
      target-found: "$player$ is $blocks$ block(s) away from you."
lobby:
  countdown: "Game will start in $sec$ second(s)!"
  countdowncancel: "بحاجة إلى المزيد من اللاعبين. تم إلغاء العد التنازلي!"
  cancelcountdown:
    not_enough_players: "بحاجة إلى المزيد من اللاعبين. تم إلغاء العد التنازلي!"
    not_enough_teams: "بحاجة إلى مزيد من الفرق. تم إلغاء العد التنازلي!"
  cancelstart:
    not_enough_players: "يلزم المزيد من اللاعبين لبدء اللعبة!"
    not_enough_teams: "فرق أكثر مطلوبة لبدء تشغيل اللعبة!"
  chooseteam: "اختر فريق"
  startgame: "بدء اللعبة"
  reduce_countdown: "تقليل العد التنازلي"
  gamefull: "لعبة ممتلئ!"
  gamefullpremium: "اللعبة بالكامل من اللاعبين الداعمين بالفعل!"
  teamjoined: "لقد انضممت الى الفريق $team$ بنجاح"
  leavegame: "ترك اللعبة"
  playerjoin: "Player $player$ joined the game!"
  kickedbyvip: "You were kicked by a vip player which has joined the full game!"
  moreplayersneeded: "$count$ more players needed."
  moreplayersneeded-one: "$count$ more player needed."
  moreteamsneeded: "A minimum of two players in two different teams is needed to start the game!"
sign:
  firstline: "&6[Bedwars]"
  players: "اللاعبين"
  gamestate:
    stopped: "&4Stopped!"
    waiting: "&aWaiting ..."
    running: "&9Running!"
    full: "Full!"
stats:
  header: "Bedwars Stats"
  kd: "K/D"
  statsnotfound: "Statistics of $player$ not found!"
  name: "Name"
  kills: "Kills"
  deaths: "Deaths"
  wins: "عدد مرات الفوز"
  loses: "عدد مرات الخسارة"
  score: "النقاط"
  destroyedBeds: "الاسرة المدمرة"
  games: "الألعاب"
commands:
  addgame:
    name: "إضافة لعبة"
    desc: "إضافة لعبة جديدة"
  addteam:
    name: "إضافة فريق"
    desc: "Adds a team to a specific game"
  join:
    name: "الانضمام إلى لعبة"
    desc: "Joins a specific game"
  leave:
    name: "ترك اللعبة"
    desc: "Leave the current game"
  save:
    name: "حفظ اللعبة"
    desc: "Saves a game (and map) to config file(s)"
  settarget:
    name: "تحديد الهدف"
    desc: "Sets the location of a team's target block"
  setbed:
    name: "Set bed (synonym for settarget)"
    desc: "Sets the location of a team's bed block (Synonym for settarget)"
  setlobby:
    name: "Set lobby"
    desc: "Sets the location of the gamelobby"
  setregion:
    name: "Sets a region point"
    desc: "Sets a region point for the game"
  setspawn:
    name: "Sets a team spawn"
    desc: "Sets the spawn of the given team"
  setspawner:
    name: "Set spawner"
    desc: "Sets a spawner location of a specific ressource"
  start:
    name: "بدء اللعبة"
    desc: "Starts a game"
  stop:
    name: "Stop game"
    desc: "Stops a game"
  help:
    name: "Show Help"
    desc: "Display information about the plugin and its commands"
  reload:
    name: "Reload"
    desc: "Reloads the configurations and translations"
  setmainlobby:
    name: "Set main lobby"
    desc: "Sets the main lobby of a game (is needed when mainlobby-enabled is set to true)"
  list:
    name: "List games"
    desc: "Lists all available games"
  regionname:
    name: "Set region name"
    desc: "Sets an individual region name (instead of world name)"
  removeteam:
    name: "Remove team"
    desc: "Removes a team from the game (only in stopped mode)"
  removegame:
    name: "Remove game"
    desc: "Removes a game and every configuration"
  clearspawner:
    name: "Clear spawners"
    desc: "Removes all spawners from the game. Saving needed."
  gametime:
    name: "Set game time"
    desc: "Sets the game time which should be used in the game-world"
  stats:
    name: "Statistics"
    desc: "Shows your statistics"
  setbuilder:
    name: "Sets builder of map"
    desc: "Sets the builder of the map which will be display in the title when game starts."
  setgameblock:
    name: "Set game block"
    desc: "Sets the game block type for this game which should be used instead of the 'game-block' configuration. Write 'DEFAULT' as type to use the type of config again"
  setautobalance:
    name: "Set autobalance"
    desc: "If 'global-autobalance' is set to 'false', with this command you can set team-autobalance per game to on or off!"
  setminplayers:
    name: "Set minimum players"
    desc: "Sets the amount of players needed to start the game"
  kick:
    name: "Kick player"
    desc: "Kicks a player from the current game!"
  addteamjoin:
    name: "Add team selection"
    desc: "Mark a creature which can be used to join a specific team!"
  addholo:
    name: "Add hologram location"
    desc: "A hologram statistic will be added at the current position!"
  removeholo:
    name: "Remove hologram location"
    desc: "When the command was executed, the player can right-click the hologram which should be removed."
    explain: "Perform a left click within 10 seconds on the hologram you want to remove."
  debugpaste:
    name: "Paste debug data"
    desc: "This will send some debug data to hastebin and returns a link you can share with the developers"
  itemspaste:
    name: "Paste your inventory to a file"
    desc: "This will return a link where you can see your current inventory being serialized as an example for your shop.yml"
