---
default:
  pages: "Paginas $current$ de $max$"
  currently: Currentes
errors:
  argumentslength: "Número de argumentos não coincide com a quantidade correta!"
  holodependencynotfound: "Couldn't find $dependency$ for Hologram-Statistic"
  packagenotfound: "Nao foi possivel obter $package$ package!"
  classnotfound: "Nao foi possivel obter $package$ class $class$!"
  gameexists: "Um jogo com este nome já existe!"
  gamenotfound: "O jogo '$game$' não pôde ser encontrado!"
  nofreegames: "There are no free games available."
  gamenotfoundsimple: "Jogo nao encontrado!"
  playeramount: "O maximo de jogadores não pode ser inferior a 1 ou superior a 24,!"
  teamcolornotallowed: "A determinada cor da equipe não é uma cor permitida"
  teamnamelength: "O nome da equipe deve ter entre 2 e 20 caracteres!"
  teamnotfound: "Team nao encontrada!"
  notingame: "Não está atualmente em um jogo!"
  bedtargeting: "Você tem que direcionar ou ficar em um bloco do 'jogo-bloco' tipo configurado!"
  regionargument: "Seu argumento localização tem de ser\" LOC1 'ou' LOC2 '!"
  spawnerargument: "The resource parameter has to be a valid configured resource!"
  blockdownnotfound: "O bloco que você está em pé não foi encontrado!"
  gamenotrunning: "O jogo não está a funcionar"
  bungeenoserver: "Servidores Bungeecord não foram definidos corretamente .Converse com o administrador do servidor!"
  cantstartagain: "O jogo ja comecou .Não pode iniciar um jogo de novo!"
  startoutofwaiting: "O jogo tem que ser iniciado fora do modo de espera!"
  cantjoingame: "Nao podes entrar num jogo que ja comecou ou que esta parado"
  lobbyongameworld: "Lobby não pode estar no mundo do jogo!"
  gamenotloaded: "Não foi possível iniciar o jogo!"
  gameloaderror: "Enquanto carregava o jogo '$game$' levou a um erro!"
  regionnotfound: "O arquivo da regiao nao existe!"
  savesign: "Não foi possível criar um novo arquivo de configuração sinal!"
  nogames: "Nenhum jogo encontrado!"
  notenoughress: "Não tens ressource suficiente para comprar este item!"
  teamnameinuse: "O nome dessa team ja esta em uso"
  minplayersmustnumber: "O minimo de players tem de ser um numero"
  toolongregionname: "O comprimento máximo do nome da região estão 15 caracteres!"
  notwhilegamerunning: "Não é possível fazer isso enquanto jogo está sendo executado!"
  notwhileingame: "Não é possível fazer isso enquanto estas num jogo que ja comecou"
  timeincorrect: "O tempo tem que ser um número (0 ... 23000), 'dia' ou 'noite'!"
  minplayersnumeric: "O parâmetro mínimo de-jogadores deve ser numérico!"
  notinair: "Nao estas no ar"
  teamfull: "A equipa está completa, por favor reabre o menu de selecção de equipas!"
  novalidmaterial: "Tipo de bloco dado (material) nao esta correto!"
  wrongvalueonoff: "Parametro errado! Usa true,em,1 para ativar - Usa false,em,0 para desativar!"
  playernotfound: "Dado jogador não foi encontrado ou não está on-line!"
  notingameforkick: "Deves estar em jogo para kickares um jogador"
  playernotingame: "Dado jogador nao esta no jogo!"
  mustbeinlobbyworld: "You must be in the lobby world of the game!"
  addteamjoincancel: "The adding of team join was cancelled!"
  entitynotcompatible: "This entity is not compatible with team join!"
success:
  gameadded: "Novo jogo '$game$' adicionado com sucesso"
  teamadded: "Team '$team$' adicionada com sucesso"
  joined: "Entraste no jogo!"
  left: "Saiste do jogo!"
  saved: "O jogo foi salvado com sucesso"
  bedset: "Setaste o bloco de respawn com sucesso na team $team$!"
  regionset: "Game region location $location$ for Game $game$ was set successfully!"
  spawnset: "O spawn da team $team$ foi setado com sucesso"
  spawnerset: "O spawn da ressource $name$ foi adicionada com sucesso!"
  stopped: "O jogo foi parado com sucesso!"
  lobbyset: "Lobby foi setado com sucesso!"
  gameloaded: "O jogo '$game$' foi carregado com sucesso!"
  reloadconfig: "Reload com sucesso!"
  teamremoved: "Team foi removida com sucesso!"
  gameremoved: "O jogo foi removido com sucesso"
  spawnercleared: "Todas as ressources foram removidas"
  gamerun: "Iniciaste o jogo, agora os jogadores podem entrar!"
  timeset: "O tempo do jogo foi setado com sucesso!"
  regionnameset: "O nome da regiao foi definido"
  minplayersset: "O minimo de players foram setados"
  mainlobbyset: "O Mainlobby foi setado"
  gametimeset: "O tempo do jogo foi setado!"
  materialset: "O tipo do bloco de respawn (material) foi definido!"
  builderset: "O builder do mapa foi setado e vai aperecer no titulo do jogo"
  autobalanceseton: "Autobalance foi ativado!"
  autobalancesetoff: "Autobalance foi &cdesativado!"
  selectteamjoinentity: "Now do a right click on the entity you want to use as team join!"
  teamjoinadded: "Entity was successfully marked as team selection for team $team$"
  holoremoved: "Hologram-Statistic successfully removed!"
gamecheck:
  LOC_NOT_SET_ERROR: "Locais para a região não foram definidas corretamente!"
  TEAM_SIZE_LOW_ERROR: "Tens de adicionar mais teams"
  NO_RES_SPAWNER_ERROR: "Nao adicionaste nenhum ressource!"
  NO_LOBBY_SET: "Ainda nao definiste o lobby!"
  TEAMS_WITHOUT_SPAWNS: "Há teams sem o spawn setado"
  NO_ITEMSHOP_CATEGORIES: "Nenhuma categoria da itemshop encontrada"
  NO_MAIN_LOBBY_SET: "Ainda nao definiste o main lobby ja que na config meteste 'tomainlobby' para true"
  TEAM_NO_WRONG_BED: "Uma ou mais teams nao tem cama setada"
  TEAM_NO_WRONG_TARGET: "Uma ou mais teams nao tem o bloco de respawn setado!"
ingame:
  team: "Team"
  teams: "Teams"
  all: "Todos"
  record: "&e$record$&a é o tempo recorde deste mapa!"
  record-with-holders: '&aO recorde deste mapa &e$record$&a e foi feito por: $holders$'
  newrecord: '&aTeam $team$&a realizou um novo recorde: &6$record$ Parabens!!'
  record-nobeddestroy: "&cRecorde nao sera guardado poque nunhuma cama foi destruida"
  teamwon: "Parabens! Team $team$ ganhou!"
  draw: "O jogo acaba com um empate :o!"
  serverrestart: "Servidor reiniciando em  $sec$ segundo(s)!"
  gamestarting: "Jogo começando ..."
  gamestarted: "Jogo '$game$' acabou de começar!"
  backtolobby: "De volta ao spawn em $sec$ segundo(s)!"
  spectator: "Spectator"
  spectate: "&aVer"
  teamchest: "Bau da Team"
  noturteamchest: "Este bau nao é um bau da tua team!"
  protectionleft: "Invencivel por &c$length$&f segundo(s)!"
  protectionend: "Voce agora é &cvulneravel&f de novo!"
  team-dead: "Team $team$ foi destruida!"
  no-friendlybreak: "&cNao podes partir blocos debaixo de um membro da tua team!"
  teamchestdestroy: "&cOne of your teamchest(s) has been destroyed!"
  title:
    map-builder: "Contruido por $builder$"
    win-title: "&6Parabens!"
    win-subtitle: "$team$&6 ganhou em &e$time$"
  shop:
    name: "Loja"
    newshop: "Usar a nova Loja"
    oldshop: "Usar a antiga Loja"
    fullstackpershift: "Multiplica os stacks por cada click no shift"
    onestackpershift: "Um stack por cada click no shift"
  player:
    left: "O jogador $player$ deixou o jogo!"
    died: "$player$ morreu!"
    killed: "$killer$ matou $player$!"
    kicked: "$player$ foi kickado do jogo!"
    waskicked: "Foste kickado do jogo!"
  blocks:
    ownbeddestroy: "Nao podes destruir a tua propria cama!"
    beddestroyed: "$player$ destruio a cama da team $team$!"
  specials:
    rescue-platform:
      left: "Há &c$time$&f segundo(s) ate poderes usar outra vez a plataforma de resgate!"
    arrow-blocker:
      start: "You are protected from being hit by arrows for &c$time$&f second(s)."
      end: "&cYour arrow protection is over"
      left: "There are &c$time$&f second(s) left until you can use the next arrowblocker!"
    trap:
      trapped: "&eAlguem caiu numa &ctrap&e da tua team!"
    protection-wall:
      left: "Há &c$time$&f segundo(s) ate poderes usar outra vez a parede de protecçao"
      not-usable-here: "Nao podes usar a parede de protecçao aí"
    warp-powder:
      cancelled: "&cO teu teleporte foi cancelado!"
      start: "Iras ser teleportado em &c$time$&f segundo(s). Nao te mexas!"
      cancel: "&4Cancelar o teleporte"
      multiuse: "&cYou started a teleportation already!"
    tntsheep:
      no-target-found: "Nenhum player encontrado"
    tracker:
      no-target-found: "Nenhum player encontrado"
      target-found: "$player$ is $blocks$ block(s) away from you."
lobby:
  countdown: "O jogo vai começar em $sec$ segundo(s)!"
  countdowncancel: "Mais players precisos. A countdown foi cancelada!"
  cancelcountdown:
    not_enough_players: "Mais jogadores precisos. Countdown cancelada!"
    not_enough_teams: "More teams needed. Countdown cancelled!"
  cancelstart:
    not_enough_players: "Mais jogadores sao precisos para o jogo começar!"
    not_enough_teams: "More teams are needed to start the game!"
  chooseteam: "Escolhe uma team"
  startgame: "Comecar o jogo"
  reduce_countdown: "Reduce countdown"
  gamefull: "Jogo esta cheio"
  gamefullpremium: "O jogo esta cheio de jogadores premium!"
  teamjoined: "Entraste na team $team$"
  leavegame: "Deixar o jogo"
  playerjoin: "O jogador $player$ entrou no jogo!"
  kickedbyvip: "Foste kickado porque um jogador premium entrou!"
  moreplayersneeded: "$count$ more players needed."
  moreplayersneeded-one: "$count$ more player needed."
  moreteamsneeded: "A minimum of two players in two different teams is needed to start the game!"
sign:
  firstline: "&6[Bedwars]"
  players: "Jogadores"
  gamestate:
    stopped: "&4[Desativado]"
    waiting: "&a[Entrar]"
    running: "&9[Em jogo]"
    full: "Cheio!"
stats:
  header: "Estatisticas do Bedwars"
  kd: "K/D"
  statsnotfound: "Estatisticas de $player$ nao encontradas!"
  name: "Nome"
  kills: "Kills"
  deaths: "Mortes"
  wins: "Vitorias"
  loses: "Perdidos"
  score: "Score"
  destroyedBeds: "Camas destruidas"
  games: "Jogos"
commands:
  addgame:
    name: "Add Game"
    desc: "Adiciona um novo jogo"
  addteam:
    name: "Add Team"
    desc: "Adiciona uma nova team num determinado mapa"
  join:
    name: "Join Game"
    desc: "Entra num jogo de um mapa determinado"
  leave:
    name: "Leave Game"
    desc: "Sais do jogo corrente"
  save:
    name: "Save Game"
    desc: "Guarda o jogo (e mapa) para os ficheiros da config"
  settarget:
    name: "Set target"
    desc: "Seta a localização do bloco de respawn"
  setbed:
    name: "Set bed (synonym for settarget)"
    desc: "Seta a localização da cama da team (Igual ao settarget)"
  setlobby:
    name: "Set lobby"
    desc: "Seta a localização do lobby de um jogo"
  setregion:
    name: "Sets a region point"
    desc: "Seta um ponto da regiao de um jogo"
  setspawn:
    name: "Sets a team spawn"
    desc: "Seta o spawn dado por uma team"
  setspawner:
    name: "Set spawner"
    desc: "Seta a localização do spawn de uma especifica ressource"
  start:
    name: "Comecar o jogo"
    desc: "Começa o jogo"
  stop:
    name: "Stop game"
    desc: "Para um jogo"
  help:
    name: "Show Help"
    desc: "Da informaçao sobre o plugin e comandos"
  reload:
    name: "Reload"
    desc: "Ira dar reload ao plugin"
  setmainlobby:
    name: "Set main lobby"
    desc: "Seta o mainlobby (é preciso que na config o mainlobby-enabled esteja true)"
  list:
    name: "List games"
    desc: "Lista de jogos disponiveis"
  regionname:
    name: "Set region name"
    desc: "Seta o nome do mapa na tabuleta (em vez do nome do mundo)"
  removeteam:
    name: "Remove team"
    desc: "Remove uma team de um jogo (somente funciona se o jogo estiver parado)"
  removegame:
    name: "Remove game"
    desc: "Remove um jogo e a sua configuracao"
  clearspawner:
    name: "Clear spawners"
    desc: "Remove todos os spawners de um jogo. Precisa de ser guardado."
  gametime:
    name: "Set game time"
    desc: "Seta o tempo de jogo de um determinado jogo"
  stats:
    name: "Statistics"
    desc: "Mostra as estatisticas"
  setbuilder:
    name: "Sets builder of map"
    desc: "Seta o builder de um mapa que sera mostrado em titulo quando o jogo comecar."
  setgameblock:
    name: "Set game block"
    desc: "Seta o tipo de bloco de jogo desse jogo que deveria ser usado em vez do que esta configurado na config. Escreve 'DEFAULT' como tipo para usares o tipo de config outra vez"
  setautobalance:
    name: "Set autobalance"
    desc: "Se 'global-autobalance' esta setado 'false', com este comando tu podes setar team-autobalance por jogo em ativado ou desativado!"
  setminplayers:
    name: "Set minimum players"
    desc: "Sets the amount of players needed to start the game"
  kick:
    name: "Kick player"
    desc: "Kicka um jogador do seu jogo corrente!"
  addteamjoin:
    name: "Add team selection"
    desc: "Mark a creature which can be used to join a specific team!"
  addholo:
    name: "Add hologram location"
    desc: "A hologram statistic will be added at the current position!"
  removeholo:
    name: "Remove hologram location"
    desc: "When the command was executed, the player can right-click the hologram which should be removed."
    explain: "Perform a left click within 10 seconds on the hologram you want to remove."
  debugpaste:
    name: "Paste debug data"
    desc: "This will send some debug data to hastebin and returns a link you can share with the developers"
  itemspaste:
    name: "Paste your inventory to a file"
    desc: "This will return a link where you can see your current inventory being serialized as an example for your shop.yml"
