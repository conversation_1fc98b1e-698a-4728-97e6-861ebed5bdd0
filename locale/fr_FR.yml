---
default:
  pages: "Page $current$ sur $max$"
  currently: Actuellement
errors:
  argumentslength: "Le nombre d'arguments ne correspond pas au nombre nécessaire !"
  holodependencynotfound: "Impossible de trouver $dependency$ pour l'hologramme des statistiques"
  packagenotfound: "Impossible de récupérer le package $package$ !"
  classnotfound: "Impossible de récupérer la classe $class$ du package $package$ !"
  gameexists: "Une partie portant ce nom existe déjà!"
  gamenotfound: "La partie '$game$' est introuvable !"
  nofreegames: "Aucune partie disponible."
  gamenotfoundsimple: "Partie introuvale !"
  playeramount: " Le nombre de joueurs ne peut pas être inférieur à 1 ou supérieur à 24 !"
  teamcolornotallowed: "La couleur d'équipe attribué n'est pas autorisée"
  teamnamelength: "Le nom d'équipe doit contenir entre 2 et 200 caractères !"
  teamnotfound: "Equipe introuvable !"
  notingame: "Vous n'êtes pas en jeu actuellement !"
  bedtargeting: "Vous devez viser ou vous tenir sur un block du type de 'game-block' configuré !"
  regionargument: "Votre argument de position doit être 'loc1' ou 'loc2' !"
  spawnerargument: "Le paramètre de ressources doit être configuré et valide!"
  blockdownnotfound: "Le bloc sur lequel vous vous tenez n'a pas été trouvé !"
  gamenotrunning: "La partie n'est pas lancée !"
  bungeenoserver: "Le serveur Bungeecord n'a pas été configuré correctement ! Contactez un l'administrateur du serveur !"
  cantstartagain: "La partie est déjà lancée ! Vous ne pouvez pas démarrer une partie lancée !"
  startoutofwaiting: "La partie doit être lancée hors du mode d'attente !"
  cantjoingame: "Vous ne pouvez pas rejoindre une partie lancée ou arrêtée !"
  lobbyongameworld: "Le Lobby ne peut pas être sur le monde de jeu !"
  gamenotloaded: "Impossible de démarrer la partie !"
  gameloaderror: "Le chargement de la partie '$game$' renvoie une erreur!"
  regionnotfound: "Le fichier de region n'existe pas !"
  savesign: "Impossible de créer une nouvelle configuration de panneau !"
  nogames: "Aucune partie trouvée !"
  notenoughress: "Vous n'avez pas assez de ressources pour acheter cet item !"
  teamnameinuse: "Ce nom d'équipe est déjà utilisé !"
  minplayersmustnumber: "Le nombre minimal de joueurs doit être un nombre !"
  toolongregionname: "La longueur maximal des noms de régions est 15 caractères !"
  notwhilegamerunning: "Impossible de faire cela quand la partie est en cours !"
  notwhileingame: "Vous ne pouvez pas faire ça quand vous êtes dans une partie est en cours !"
  timeincorrect: "Le temps doit être un nombre (0 ... 23000),'day' ou 'night' !"
  minplayersnumeric: "Le paramètre de joueurs minimals doit être numérique!"
  notinair: "Vous n'êtes pas en l'air!"
  teamfull: "L'équipe est complète, ouvrez à nouveau le menu de sélection d'équipe !"
  novalidmaterial: "Le type de bloc (material) est incorrect !"
  wrongvalueonoff: "Paramètre incorrect ! Utilisez true, on, 1 pour allumer - utilisez false, off, 0 pour désactiver !"
  playernotfound: "Ce joueur n'a pas été trouvé ou n'est pas en ligne!"
  notingameforkick: "Vous devez être dans une partie pour éjecter un joueur !"
  playernotingame: "Le joueur n'est pas dans cette partie!"
  mustbeinlobbyworld: "You must be in the lobby world of the game!"
  addteamjoincancel: "L'ajout d'équipe a été annulé!"
  entitynotcompatible: "Cette entité n'est pas compatible avec l'équipe à rejoindre!"
success:
  gameadded: "Nouvelle partie '$game$' ajoutée avec succès !"
  teamadded: "Equipe '$team$' ajoutée avec succès !"
  joined: "Vous avez rejoins la partie avec succès !"
  left: "Vous avez quitté la partie !"
  saved: "Partie sauvegardé avec succès !"
  bedset: "Vous avez configuré avec succès le bloc de respawn de l'équie $team$ !"
  regionset: "Position de la région de jeu $location$ configuré avec succès pour la partie $game$ !"
  spawnset: "Position du spawn de l'équipe $team$ configuré avec succès !"
  spawnerset: "Position du spawn de ressouces de $name$ configuré avec succès !"
  stopped: "Partie arrêtée !"
  lobbyset: "Le Lobby a été configuré avec succès !"
  gameloaded: "Partie '$game$' chargée avec succès !"
  reloadconfig: "Rechargement términé !"
  teamremoved: "Equipe supprimée avec succès !"
  gameremoved: "Partie supprimée avec succès !"
  spawnercleared: "Tous les spawners de ressources ont été supprimés !"
  gamerun: "Vous avez démarré la partie, les joueurs peuvent maintenant rejoindre !"
  timeset: "Temps de jeu configuré avec succès !"
  regionnameset: "Nom de région configuré avec succès !"
  minplayersset: "Joueurs minimum configuré avec succès !"
  mainlobbyset: "Le Lobby principal a été configuré avec succès !"
  gametimeset: "Temps de jeu configuré avec succès !"
  materialset: "Le type de bloc (material) de respawn a été configuré avec succès !"
  builderset: "Le constructeur de la map a été configuré avec succès et va être affiché en titre !"
  autobalanceseton: "L'Autobalance a été activé avec succès!"
  autobalancesetoff: "L'Autobalance à bien été mis en &coff& &a!"
  selectteamjoinentity: "Maintenant, faites un clic droit sur l'entité que vous souhaitez utiliser comme équipe rejoindre!"
  teamjoinadded: "L'entité a été sélectionnée avec succès pour l'équipe $team$"
  holoremoved: "L'Hologramme-Statistique à bien été supprimé avec succès!"
gamecheck:
  LOC_NOT_SET_ERROR: "La position pour la région n'a pas été configurée correctement !"
  TEAM_SIZE_LOW_ERROR: "Vous devez configurer plus d'équipe !"
  NO_RES_SPAWNER_ERROR: "Vous n'avez configuré aucun spawner de ressource !"
  NO_LOBBY_SET: "Vous n'avez pas configuré de lobby !"
  TEAMS_WITHOUT_SPAWNS: "Il y a une/des équipe(s) sans position de spawn !"
  NO_ITEMSHOP_CATEGORIES: "Aucune catégorie de boutique trouvée !"
  NO_MAIN_LOBBY_SET: "Vous n'avez pas configuré de Lobby principal bien que vous ayez configuré 'tomainlobby' sur true !"
  TEAM_NO_WRONG_BED: "Une ou plusieurs équipes n'ont pas de lit configuré !"
  TEAM_NO_WRONG_TARGET: "Une ou plusieurs équipes n'ont pas de bloc de respawn configuré !"
ingame:
  team: "Equipe"
  teams: "Equipes"
  all: "Tous"
  record: "&e$record$&a est le record sur cette map !"
  record-with-holders: '&aLe record sur cette map est &e$record$&a et est détenu par : $holders$'
  newrecord: '&aL''équipe $team$&a a défini un nouveau record : &6$record$'
  record-nobeddestroy: "&cLe record ne sera pas sauvegardé, car aucun lit n'a été détruit !"
  teamwon: "Félicitations ! L'équipe $team$ a gagné !"
  draw: "La partie se termine sur un match nul !"
  serverrestart: "Le serveur redémarre dans $sec$ seconde(s) !"
  gamestarting: "La partie commence ..."
  gamestarted: "La partie '$game$' viens de commencer !"
  backtolobby: "Retour au lobby dans $sec$ seconde(s)!"
  spectator: "Spectateur"
  spectate: "&aRegarder"
  teamchest: "Coffre de l'équipe"
  noturteamchest: "Ce coffre n'est pas un coffre de votre équipe !"
  protectionleft: "Vous êtes invulnérable pendant &c$length$&f seconde(s) !"
  protectionend: "Vous êtes maintenant &cvulnerable&f à nouveau !"
  team-dead: "L'équipe $team$ a été détruite !"
  no-friendlybreak: "&cImpossible de casser des blocs sous vos coéquipiers !"
  teamchestdestroy: "&cUne de vos équipe(s) a été détruite!"
  title:
    map-builder: "Construit par $builder$"
    win-title: "&6Félicitations!"
    win-subtitle: "&6La team $team$&6 a gagné en &e$time$"
  shop:
    name: "Boutique"
    newshop: "Utilisez la nouvelle boutique"
    oldshop: "Utilisez l'ancienne boutique"
    fullstackpershift: "Multipliez les stacks avec MAJ clic"
    onestackpershift: "Un stack par MAJ clic"
  player:
    left: "Le joueur $player$ a quitté la partie !"
    died: "$player$ est mort !"
    killed: "$killer$ a tué $player$!"
    kicked: "$player$ à été kick de la partie!"
    waskicked: "Tu à été kick de la partie !"
  blocks:
    ownbeddestroy: "Vous ne pouvez pas détruire votre propre lit !"
    beddestroyed: "$player$ a détruit le lit de l'équipe $team$ !"
  specials:
    rescue-platform:
      left: "Il reste &c$time$&f seconde(s) avant que vous puissiez utiliser la prochaine plateforme de secours !"
    arrow-blocker:
      start: "Vous êtes protégé d'être frappé par des flèches pour &c$time$&f seconde(s)."
      end: "&cVotre protection contre les flèches est terminée"
      left: "Il reste &c$time$ &f seconde(s) restantes jusqu'à ce que vous pouvez utiliser le bloqueur de flèches suivant!"
    trap:
      trapped: "&eQuelqu'un est allé dans un &cpiège&e de votre équipe !"
    protection-wall:
      left: "Il reste &c$time$&f avant que vous puissiez utiliser le prochain mur de protection !"
      not-usable-here: "Vous ne pouvez pas utiliser le mur de protection ici !"
    warp-powder:
      cancelled: "&cVotre téléportation a été annulé !"
      start: "Vous allez être téléporté dans &c$time$&f seconde(s). Ne bougez pas !"
      cancel: "&4Annuler la téléportation"
      multiuse: "&cTu a déjà démarrer une téléportation!"
    tntsheep:
      no-target-found: "Aucun joueur cible n'a été trouvé!"
    tracker:
      no-target-found: "Aucun joueur cible n'a été trouvé!"
      target-found: "$player$ est à $blocks$ bloc(s) de toi."
lobby:
  countdown: "La partie commence dans $sec$ seconde(s)!"
  countdowncancel: "Plus de joueurs sont nécessaires. Compte à rebours annulé !"
  cancelcountdown:
    not_enough_players: "Plus de joueurs sont nécessaires. Compte à rebours annulé !"
    not_enough_teams: "Plus d'équipes nécessaires. Compte à rebours annulé!"
  cancelstart:
    not_enough_players: "Plus de joueurs sont nécessaires pour commencer la partie !"
    not_enough_teams: "Plus d'équipes sont nécessaires pour commencer le jeu!"
  chooseteam: "Choisir une équipe"
  startgame: "Démarrer partie"
  reduce_countdown: "Réduire le compte à rebours"
  gamefull: "La partie est complète !"
  gamefullpremium: "La partie est déjà pleine de joueurs VIP !"
  teamjoined: "Vous avez rejoins l'équipe $team$ avec succès"
  leavegame: "Quitter la partie"
  playerjoin: "Le joueur $player$ a rejoint la partie !"
  kickedbyvip: "Vous avez été éjecté de la partie par un VIP qui a rejoint la partie complète !"
  moreplayersneeded: "$count$ joueurs sont nécessaires."
  moreplayersneeded-one: "$count$ joueur est nécessaire."
  moreteamsneeded: "Un minimum de deux joueurs dans deux équipes différentes est nécessaire pour commencer le jeu!"
sign:
  firstline: "&6{Bedwars}"
  players: "Joueurs"
  gamestate:
    stopped: "&4Arrêté !"
    waiting: "&aEn Attente ..."
    running: "&9En cours !"
    full: "Complet !"
stats:
  header: "Statistiques Bedwars"
  kd: "V/M"
  statsnotfound: "Statistiques de $player$ introuvable !"
  name: "Nom"
  kills: "Victimes"
  deaths: "Morts"
  wins: "Victoires"
  loses: "Défaites"
  score: "Scores"
  destroyedBeds: "Lits Détruits"
  games: "Parties"
commands:
  addgame:
    name: "Ajouter Partie"
    desc: "Ajoute une nouvelle partie"
  addteam:
    name: "Ajouter Equipe"
    desc: "Ajoute une équipe à une partie spécifique"
  join:
    name: "Rejoindre Partie"
    desc: "Rejoindre une partie spécifique"
  leave:
    name: "Quitter Partie"
    desc: "Quitte la partie actuelle"
  save:
    name: "Sauvegarder Partie"
    desc: "Sauvegarde une partie (et map) dans la configuration"
  settarget:
    name: "Définir cible"
    desc: "Définit la position du bloc cible d'une équipe"
  setbed:
    name: "Définir lit (synonyme de settarget)"
    desc: "Définit la position du bloc lit d'une équipe (Synonyme de settarget)"
  setlobby:
    name: "Définir Lobby"
    desc: "Définit la position du lobby de jeu"
  setregion:
    name: "Définit un point de région"
    desc: "Définit un point de région pour la partie"
  setspawn:
    name: "Définit un spawn d'équipe"
    desc: "Définit le spawn de l'équipe donné"
  setspawner:
    name: "Définir spawner"
    desc: "Définit la position d'un spawner pour une ressources specifique"
  start:
    name: "Démarrer partie"
    desc: "Démarre une partie"
  stop:
    name: "Arrêter partie"
    desc: "Arrête une partie"
  help:
    name: "Montrer aide"
    desc: "Afiiche des information sur le plugin et ses commandes"
  reload:
    name: "Recharger"
    desc: "Recharge les configurations et traductions"
  setmainlobby:
    name: "Définir lobby principal"
    desc: "Définit le lobby principal d'une partie (nécessaire quand mainlobby-enabled est définit sur true)"
  list:
    name: "Lister parties"
    desc: "Liste toutes les parties disponibles"
  regionname:
    name: "Définir nom de région"
    desc: "Définit un nom de région individuel (au lieu du nom de monde)"
  removeteam:
    name: "Supprimer équipe"
    desc: "Supprime une équipe de la partie (seulement en mode arrêté)"
  removegame:
    name: "Supprimer partie"
    desc: "Supprime une partie et toutes ses configurations"
  clearspawner:
    name: "Effacer spawners"
    desc: "Supprime tous les spawners de la partie. Sauvegarde nécessaire."
  gametime:
    name: "Définir temps de jeu"
    desc: "Définit le temps de jeu qui doit être utilisé dans le monde de jeu"
  stats:
    name: "Statistiques"
    desc: "Montre vos statistiques"
  setbuilder:
    name: "Définir constructeur de map"
    desc: "Définit le constructeur de la map qui sera affiché en titre quand la partie commence."
  setgameblock:
    name: "Définir bloc de jeu"
    desc: "Défini le type de bloc qui doit être utilisé pour cette partie au lieu de la configuration 'game-block'. Ecrivez 'DEFAULT' comme type pour utiliser la configuration de nouveau"
  setautobalance:
    name: "Définir un Autobalance"
    desc: "Si 'global-autobalance' est réglé sur 'false', avec cette commande, vous pouvez définir auto-équilibrage d'équipe par jeu sur ou hors tension!"
  setminplayers:
    name: "Mettre le nombre de joueurs minimum"
    desc: "Définit la quantité de joueurs nécessaire pour démarrer le jeu"
  kick:
    name: "Exclure des joueurs"
    desc: "Exclure un ou des joueurs d'une partie!"
  addteamjoin:
    name: "Ajouter une sélection de team"
    desc: "Marquez une créature qui peut être utilisée pour rejoindre une équipe spécifique!"
  addholo:
    name: "Ajouter une position holographique"
    desc: "Une statistique d'hologramme sera ajoutée à la position actuelle!"
  removeholo:
    name: "Supprimer une position holographique"
    desc: "Une statistique d'hologramme sera ajoutée à la position actuelle."
    explain: "Effectue un clique gauche dans les 10 secondes sur l'hologramme que vous souhaitez supprimer."
  debugpaste:
    name: "Coller les données de débogage"
    desc: "Cette option enverra des informations de debug à Hastebin et vous donnera un lien que vous pourrez partager avec les devellopeurs"
  itemspaste:
    name: "Collez votre inventaire dans un fichier"
    desc: "Cela renverra un lien où vous pouvez voir votre inventaire actuel sérialisé comme exemple pour votre shop.yml"
