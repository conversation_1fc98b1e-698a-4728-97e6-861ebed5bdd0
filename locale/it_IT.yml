---
default:
  pages: "Pagina $current$ di $max$"
  currently: Attualmente
errors:
  argumentslength: "Il numero di argomenti non corrisponde alla corretta quantità!"
  holodependencynotfound: "Non riusciva a trovare $dependency$ per ologramma-statistica"
  packagenotfound: "Non poteva recuperare il pacchetto di $ $package!"
  classnotfound: "Impossibile trovare il pacchetto $package$ e la classe $class$!"
  gameexists: "Una partita con questo nome esiste già!"
  gamenotfound: "Il gioco $game$ non può essere trovato!"
  nofreegames: "Non ci sono giochi liberi disponibili."
  gamenotfoundsimple: "Partita non trovata!"
  playeramount: "Il massimo dei player non può essere minore di 1 e maggiore di 24!"
  teamcolornotallowed: "Il colore che hai dato non è stato trovato"
  teamnamelength: "Il nome deve essere lungo minimo 2 lettere e massimo 20 lettere!"
  teamnotfound: "Squadra non trovata!"
  notingame: "Non sei attualmente in un gioco!"
  bedtargeting: "Devi indirizzare o stare in piedi su un blocco del tipo 'gioco-blocco' configurato!"
  regionargument: "Il tuo argomento di posizione deve essere 'luogo1' o 'loc2'!"
  spawnerargument: "Il parametro di risorsa deve essere una valida risorsa configurata!"
  blockdownnotfound: "Non è stato trovato il blocco che ci sei sopra!"
  gamenotrunning: "Gioco non è in esecuzione!"
  bungeenoserver: "Bungeecord server non è stato impostato correttamente! Parlare con l'amministratore del server!"
  cantstartagain: "Gioco è in esecuzione! Non è possibile avviare un gioco che ancora una volta!"
  startoutofwaiting: "Gioco deve essere avviato dalla modalità di attesa!"
  cantjoingame: "Non è possibile aggiungere un gioco in esecuzione o arrestato!"
  lobbyongameworld: "Hall non può essere il mondo di gioco!"
  gamenotloaded: "Non poteva iniziare il gioco!"
  gameloaderror: "Caricamento gioco '$game$ ' genera un errore!"
  regionnotfound: "File di regione non esiste!"
  savesign: "Impossibile creare un nuovo file di configurazione di segno!"
  nogames: "Nessun gioco trovato!"
  notenoughress: "Non hai abbastanza risorse per acquistare questo oggetto!"
  teamnameinuse: "Nome della squadra è già in uso!"
  minplayersmustnumber: "Minimo giocatori devono essere un numero!"
  toolongregionname: "Lunghezza massima del nome della regione sono 15 caratteri!"
  notwhilegamerunning: "Non puoi farlo mentre il gioco è in esecuzione!"
  notwhileingame: "Non puoi farlo mentre sei in un gioco di corsa!"
  timeincorrect: "Tempo deve essere un numero (0... 23000), 'giorno' o 'notte'!"
  minplayersnumeric: "Il parametro min-giocatori deve essere numerico!"
  notinair: "Tu non sei nell'aria!"
  teamfull: "La squadra è completa, si prega di riaprire il menu di selezione della squadra!"
  novalidmaterial: "Dato blocco tipo (materiale) non è corretto!"
  wrongvalueonoff: "Parametro errato! Utilizzare true, on, 1 per accendere - utilizzare false, off, 0 per disattivare!"
  playernotfound: "Dato giocatore era non trovato o non è in linea!"
  notingameforkick: "È necessario essere in un gioco a calci un giocatore!"
  playernotingame: "Dato giocatore non è in questo gioco!"
  mustbeinlobbyworld: "È necessario essere nel mondo Hall del gioco"
  addteamjoincancel: "L'aggiunta di join team è stato annullato!"
  entitynotcompatible: "Questa entità non è compatibile con join team!"
success:
  gameadded: "Nuovo gioco '$game$ ' aggiunto con successo!"
  teamadded: "Squadra '$team$ ' aggiunto con successo!"
  joined: "Sei entrato nella partita con successo!"
  left: "Hai lasciato la partita con successo!"
  saved: "Partita salvata con successo!"
  bedset: "Hai settato con successo il blocco di spawn del team $team$!"
  regionset: "La regione della partita settata su $location$ per la partita $game$ è stata settata con successo!"
  spawnset: "Posizione di spawn per il team $team$ è stata settata con successo!"
  spawnerset: "Spawn per la risorsa $name$ settato con successo!"
  stopped: "Partita fermata con successo!"
  lobbyset: "Lobby settata con successo!"
  gameloaded: "Partita $game$ caricata con successo!"
  reloadconfig: "Ricaricato con successo!"
  teamremoved: "Team rimosso con successo!"
  gameremoved: "Partita rimossa con successo!"
  spawnercleared: "Tutte gli spawner per le risorse sono stati rimossi!"
  gamerun: "Hai abbiato la partita, i giocatori possono entrare adesso!"
  timeset: "Tempo per la partita settato con successo!"
  regionnameset: "Nome dell' area settato con successo!"
  minplayersset: "Minimi player settati con successo!"
  mainlobbyset: "Mainlobby settata con successo!"
  gametimeset: "Tempo della partita settato con successo!"
  materialset: "Il tipo di blocco di respawn (materiale) è stato creato con successo!"
  builderset: "Il costruttore per la mappa è stato settato con successo e verrà mostrato nel titolo!"
  autobalanceseton: "Soldi automatici abilitati!"
  autobalancesetoff: "Soldi automatici &cdisabilitati &acon successo!"
  selectteamjoinentity: "Adesso clicca con il tasto destro sull' entità che vuoi usare come selettore per i team!"
  teamjoinadded: "Entità settata con successo e impostata come selettore per il team $team$"
  holoremoved: "Statistiche sull' ologramma rimosse con successo!"
gamecheck:
  LOC_NOT_SET_ERROR: "La zona per la regione non è settata correttamente!"
  TEAM_SIZE_LOW_ERROR: "Devi impostare più team!"
  NO_RES_SPAWNER_ERROR: "Non hai impostato lo spawn di nessuna risorsa!"
  NO_LOBBY_SET: "Non hai settato una lobby!"
  TEAMS_WITHOUT_SPAWNS: "Ci sono team senza una zona su cui spawnare!"
  NO_ITEMSHOP_CATEGORIES: "Nessuna categoria trovata nello shop!"
  NO_MAIN_LOBBY_SET: "Non hai settato una mainlobby oppure non hai settato 'tomainlobby' su true"
  TEAM_NO_WRONG_BED: "Uno o più team non hanno il letto impostato!"
  TEAM_NO_WRONG_TARGET: "Uno o più team non hanno il blocco di respawn settato!"
ingame:
  team: "Squadra"
  teams: "Squadre"
  all: "Globale"
  record: "&e$record$ &aè il record su questa mappa!"
  record-with-holders: '&aIl record di questa mappa è &e$record$&a ed è mantenuto da $holders$'
  newrecord: '& aTeam $team$ & un insieme un nuovo record: & 6$record$'
  record-nobeddestroy: "& cRecord non verranno salvate, perché nessun letto è stata distrutta!"
  teamwon: "Congratulazioni! Squadra $team dollari vinti!"
  draw: "Il gioco è finito in pareggio!"
  serverrestart: "Riavvio del server in $sec$ secondi!"
  gamestarting: "Gioco a partire..."
  gamestarted: "Gioco '$game$ ' è appena iniziato!"
  backtolobby: "Torna alla lobby in $sec$ secondi!"
  spectator: "Spettatori"
  spectate: "& aSpectate"
  teamchest: "Cesta di squadra"
  noturteamchest: "Questo cesta non è una cassa della tua squadra!"
  protectionleft: "Invulnerabilità per & c$length$ & f secondi!"
  protectionend: "Ora sei ancora &cvulnerable&f!"
  team-dead: "La squadra $team$ è stato distrutto!"
  no-friendlybreak: "& cNon puoi piazzare i blocchi sotto il membro del tuo team!"
  teamchestdestroy: "&c La cassa di squadra è stata distrutta!"
  title:
    map-builder: "Costruito da $builder$"
    win-title: "& 6Congratulazioni!"
    win-subtitle: "$team$ & 6 ha vinto & e$time$"
  shop:
    name: "Venditore"
    newshop: "Utilizza il nuovo negozio"
    oldshop: "Vecchio negozio in uso"
    fullstackpershift: "Moltiplica stack per ogni click dello shift"
    onestackpershift: "Fare clic su uno stack per turno"
  player:
    left: "Il giocatore $player$ ha lasciato il gioco!"
    died: "$player$ è morto!"
    killed: "$killer$ ha ucciso $player$!"
    kicked: "$player$ è stato espulso!"
    waskicked: "Sei stato cacciato dal gioco!"
  blocks:
    ownbeddestroy: "Non puoi distruggere il tuo letto!"
    beddestroyed: "$player$ ha distrutto il letto della squadra $team$!"
  specials:
    rescue-platform:
      left: "In altri & c$time$ & f secondi potrai usare un'altra piattaforma!"
    arrow-blocker:
      start: "Sei protetto dalle freccie & c$time$ & f secondi."
      end: "&cLa protezione dalle freccie è terminata"
      left: "In & c$time$ & f secondi potrai usare un'altro arroblocker!"
    trap:
      trapped: "& eQualcuno è salito su una &ctrap&e della tua squadra!"
    protection-wall:
      left: "Attenti & c$time$ & f secondi per creare un altro muro protettivo!"
      not-usable-here: "Non è possibile utilizzare il muro di protezione qui!"
    warp-powder:
      cancelled: "&c Il tuo teletrasporto è stato annullato!"
      start: "Sarai teletrasportato in & c$time$ & f secondi. Non ti muovere!"
      cancel: "&4 teletrasporto cancellato"
      multiuse: "&cHai già iniziato un teletrasporto!"
    tntsheep:
      no-target-found: "Nessun bersaglio identificato!"
    tracker:
      no-target-found: "Nessun bersaglio identificato!"
      target-found: "$player$ è $blocks$ blocchi lontano da te."
lobby:
  countdown: "Il gioco inizierà in $sec$ secondi!"
  countdowncancel: "Più giocatori necessari. Il conto alla rovescia è stato annullato!"
  cancelcountdown:
    not_enough_players: "Più giocatori necessari. Conto alla rovescia annullato!"
    not_enough_teams: "Sono necessarie più squadre. Conto alla rovescia annullato!"
  cancelstart:
    not_enough_players: "Servono più utenti per avviare la partita!"
    not_enough_teams: "Servono più team per avviare la partita!"
  chooseteam: "Scegli un team"
  startgame: "Avvia il gioco"
  reduce_countdown: "Riduci il conto alla rovescia"
  gamefull: "La partita è piena!"
  gamefullpremium: "La partita è già piena di giocatori premium!"
  teamjoined: "Sei entrato nel team $team$ con successo"
  leavegame: "Esci dalla partita"
  playerjoin: "Il giocatore $player$ è entrato!"
  kickedbyvip: "Sei stato kikkato da un giocatore vip che è entrato nella partita piena!"
  moreplayersneeded: "Servono ancora $count$ players."
  moreplayersneeded-one: "Serve ancora $count$ player."
  moreteamsneeded: "Un minimo di 2 players in 2 team diversi sono necessari per avviare il gioco!"
sign:
  firstline: "&6[Bedwars]"
  players: "Giocatori"
  gamestate:
    stopped: "&4Fermata!"
    waiting: "&cAspettando..."
    running: "&9Avviata!"
    full: "&4Piena!"
stats:
  header: "Statistiche delle BedWars"
  kd: "Uccisioni/Morti"
  statsnotfound: "Statistiche del giocatore $player$ non trovate!"
  name: "Nome"
  kills: "Uccisioni"
  deaths: "Morti"
  wins: "Vittorie"
  loses: "Perse"
  score: "Punteggio"
  destroyedBeds: "Letti distrutti"
  games: "Partite"
commands:
  addgame:
    name: "Aggiungi una partita"
    desc: "Aggiungi una nuova partita"
  addteam:
    name: "Aggiungi una squadra"
    desc: "Aggiungi una squadra ad una partita specifica"
  join:
    name: "Entra nella partita"
    desc: "Entra in un gioco specifico"
  leave:
    name: "Esci dalla partita"
    desc: "Esci dalla partita corrente"
  save:
    name: "Salva la partita"
    desc: "Salva la partita (e la mappa) nei config"
  settarget:
    name: "Imposta l' obbiettivo"
    desc: "Imposta la zona dell' obbiettivo del team"
  setbed:
    name: "Imposta il letto"
    desc: "Imposta la zona del letto della squadra"
  setlobby:
    name: "Imposta la lobby"
    desc: "Imposta la zona della lobby della partita"
  setregion:
    name: "Imposta un punto della regione"
    desc: "Imposta un punto per la regione della partita"
  setspawn:
    name: "Imposta uno spawn per il team"
    desc: "Imposta lo spawn del team specificato"
  setspawner:
    name: "Set spawner"
    desc: "Imposta una posizione spawner di un specifico materiale"
  start:
    name: "Avvia il gioco"
    desc: "Inizia una partita"
  stop:
    name: "Interrompere il gioco"
    desc: "Ferma un gioco"
  help:
    name: "Visualizza la guida"
    desc: "Visualizzare informazioni sui plugin e i relativi comandi"
  reload:
    name: "Riavvia"
    desc: "Consente di ricaricare le configurazioni e le traduzioni"
  setmainlobby:
    name: "Impostare la lobby principale"
    desc: "Imposta la lobby principale di un gioco (quando abiliti questa cosa la mainlobby deve essere impostata su true)"
  list:
    name: "Elenco giochi"
    desc: "Elenca tutti i giochi disponibili"
  regionname:
    name: "Nome dell'area"
    desc: "Imposta un nome di singola regione (anziché il nome del mondo)"
  removeteam:
    name: "Rimuovere la squadra"
    desc: "Rimuove una squadra dal gioco (solo in modalità di interruzione)"
  removegame:
    name: "Rimuovere il gioco"
    desc: "Rimuove un gioco e ogni configurazione"
  clearspawner:
    name: "Spawner eliminati"
    desc: "Rimuove tutti i riproduttori dal gioco. Risparmio necessari."
  gametime:
    name: "Tempo di gioco"
    desc: "Imposta il tempo di gioco che deve essere utilizzato nel gioco-mondo"
  stats:
    name: "Statistiche"
    desc: "Mostra le vostre statistiche"
  setbuilder:
    name: "Costruttore della mappa"
    desc: "Imposta il costruttore della mappa che sarà visualizzato nel titolo quando il gioco inizia."
  setgameblock:
    name: "Impostare il blocco del gioco"
    desc: "Imposta il tipo di gioco blocco per questo gioco che dovrebbe essere usato anziché la configurazione di 'gioco-blocco'. Scrivere 'DEFAULT' come tipo di utilizzare di nuovo il tipo di configurazione"
  setautobalance:
    name: "Set autobalance"
    desc: "Se 'global-autobalance' è impostato a 'false', con questo comando che è possibile impostare la squadra-autobalance a partita a on o off!"
  setminplayers:
    name: "Impostare il minimo di giocatori"
    desc: "Imposta la quantità di giocatori necessari per iniziare il gioco"
  kick:
    name: "Espelle un giocatore"
    desc: "Espelle un giocatore dal gioco corrente!"
  addteamjoin:
    name: "Aggiungi selezione squadra"
    desc: "Segnala una creatura che può essere utilizzata per entrare in un team specifico!"
  addholo:
    name: "Aggiungi la posizione dell'ologramma"
    desc: "Un ologramma verrà aggiunto nella posizione corrente !"
  removeholo:
    name: "Rimuovere la posizione di ologramma"
    desc: "Quando il comando viene eseguito, il giocatore può fare clic destro l'ologramma che dovrebbe essere rimosso."
    explain: "Eseguire un click sinistro entro 10 secondi l'ologramma che si desidera rimuovere."
  debugpaste:
    name: "Incolla i dati di debug"
    desc: "Questo maderà qualche stringa del debug a hastebin e ti ritornerà come link che potrai condividere con gli sviluppatori"
  itemspaste:
    name: "Incolla il tuo inventario in un file"
    desc: "Questo restituirà un collegamento in cui è possibile vedere che l'inventario corrente viene serializzato come esempio per il tuo shop.yml"
