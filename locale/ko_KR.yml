---
default:
  pages: "페이지 $current$ / $max$"
  currently: 현재
errors:
  argumentslength: "커맨드의 인자 수가 부족합니다!"
  holodependencynotfound: "홀로그램 통계를 사용하려면 $dependency$ 을/를 서버에 설치하세요."
  packagenotfound: "$package$ 패키지를 불러올 수 없습니다!"
  classnotfound: "$package$ 패키지의 $class$ 클래스를 불러올 수 없습니다!"
  gameexists: "해당 이름의 게임이 이미 존재합니다!"
  gamenotfound: "'$game$' 게임을 찾을 수 없습니다!"
  nofreegames: "현재 참여 가능한 게임이 없습니다."
  gamenotfoundsimple: "게임을 찾을 수 없습니다!"
  playeramount: "최대 인원수는 1~24명 이내로 정하십시오!"
  teamcolornotallowed: "허용된 색상 값을 입력하십시오!"
  teamnamelength: "팀 이름의 길이는 2~20 char 이내로 허용됩니다!"
  teamnotfound: "팀을 찾을 수 없습니다!"
  notingame: "당신은 게임에 참여하지 않았습니다!"
  bedtargeting: "설정에서 정의된 'game-block' 의 블럭을 바라보거나 그 위에 서 있어야 합니다!"
  regionargument: "위치 값 인자는 loc1 또는 loc2 입니다."
  spawnerargument: "리소스 파라미터는 설정에서 정의된 것으로 입력해야 합니다!"
  blockdownnotfound: "대상의 블럭을 밟고 있어야 합니다!"
  gamenotrunning: "게임이 실행중이지 않습니다!"
  bungeenoserver: "번지코드 서버 세팅이 완료되지 않았습니다! 관리자에게 문의하십시오."
  cantstartagain: "해당 게임은 이미 실행중입니다!"
  startoutofwaiting: "게임이 아직 대기 모드입니다!"
  cantjoingame: "게임이 점검중이거나 진행중입니다!"
  lobbyongameworld: "게임 맵이 있는 월드에 로비를 설정할 수 없습니다!"
  gamenotloaded: "게임을 실행하는데 실패했습니다!"
  gameloaderror: "'$game$' 게임을 불러오는 중 오류가 발생했습니다!"
  regionnotfound: "영역 파일이 존재하지 않습니다!"
  savesign: "표지판 데이터 파일을 생성할 수 없었습니다!"
  nogames: "참여 가능한 게임이 없습니다!"
  notenoughress: "아이템을 구매할 자원이 부족합니다!"
  teamnameinuse: "그 팀 이름은 이미 사용중입니다!"
  minplayersmustnumber: "최소 인원으로 정수 값을 입력하세요!"
  toolongregionname: "영역 이름의 길이는 최대 15 char 입니다!"
  notwhilegamerunning: "아직 게임이 실행중입니다!"
  notwhileingame: "우선 참여한 게임을 나가세요!"
  timeincorrect: "시간 값은 정수 (0 ~ 23000), 'day' 또는 'night' 만 허용됩니다."
  minplayersnumeric: "최소 유저 수는 정수 값으로 입력하십시오!"
  notinair: "공중에 떠 있어야 합니다!"
  teamfull: "해당 팀의 인원수가 만원입니다, 다른 팀에 참가하세요!"
  novalidmaterial: "해당 블럭 종류(재질) 값이 유효하지 않습니다!"
  wrongvalueonoff: "잘못된 인자 값입니다! 기능 켜기: true/on/1 - 기능 끄기: false/off/0"
  playernotfound: "그 유저는 접속중이지 않거나 존재하지 않습니다!"
  notingameforkick: "게임에 참여한 후 유저를 퇴장시킬 수 있습니다!"
  playernotingame: "그 유저는 이 게임에 없습니다!"
  mustbeinlobbyworld: "해당 게임의 로비에 있어야 합니다"
  addteamjoincancel: "팀 참여기 생성이 취소되었습니다!"
  entitynotcompatible: "팀 참여기와 호환되는 엔티티가 아닙니다!"
success:
  gameadded: "새로운 게임 '$game$' 이 생성되었습니다!"
  teamadded: "'$team$' 팀이 추가되었습니다!"
  joined: "게임에 참여했습니다!"
  left: "게임에서 나갔습니다!"
  saved: "게임 설정을 저장했습니다!"
  bedset: "$team$ 팀의 리스폰 블럭을 지정했습니다!"
  regionset: "$game$ 게임 영역의 $location$ 위치를 지정했습니다!"
  spawnset: "$team$ 팀의 스폰 위치를 지정했습니다!"
  spawnerset: "$name$ 자원 생성기의 위치를 지정했습니다."
  stopped: "게임을 종료시켰습니다!"
  lobbyset: "로비 위치를 지정했습니다!"
  gameloaded: "'$game$' 게임을 로드했습니다!"
  reloadconfig: "성공적으로 리로드했습니다!"
  teamremoved: "팀을 제거했습니다!"
  gameremoved: "게임을 제거했습니다!"
  spawnercleared: "모든 자원 생성기를 제거했습니다!"
  gamerun: "게임을 활성화시켰습니다. 게임 참여가 가능해집니다!"
  timeset: "게임 시간을 설정했습니다!"
  regionnameset: "영역 이름을 설정했습니다!"
  minplayersset: "최소 유저 수를 설정했습니다!"
  mainlobbyset: "메인 로비 위치를 지정했습니다!"
  gametimeset: "게임 시간이 설정되었습니다!"
  materialset: "The respawn block type (material) was set succesfully!"
  builderset: "The builder for the map was set successfully and will display in title!"
  autobalanceseton: "Autobalance was successfully turned on!"
  autobalancesetoff: "Autobalance was successfully turned &coff&a!"
  selectteamjoinentity: "Now do a right click on the entity you want to use as team join!"
  teamjoinadded: "Entity was successfully marked as team selection for team $team$"
  holoremoved: "Hologram-Statistic successfully removed!"
gamecheck:
  LOC_NOT_SET_ERROR: "Locations for the region were not set properly!"
  TEAM_SIZE_LOW_ERROR: "You have to set more teams!"
  NO_RES_SPAWNER_ERROR: "You didn't set any resource spawner!"
  NO_LOBBY_SET: "You didn't set a lobby!"
  TEAMS_WITHOUT_SPAWNS: "There are team(s) without a spawn location!"
  NO_ITEMSHOP_CATEGORIES: "No itemshop categories found!"
  NO_MAIN_LOBBY_SET: "You didn't set a main lobby even though you did set 'tomainlobby' to true"
  TEAM_NO_WRONG_BED: "One or more teams have no bed set!"
  TEAM_NO_WRONG_TARGET: "One or more teams have no respawn block set!"
ingame:
  team: "Team"
  teams: "팀"
  all: "All"
  record: "&a이 맵의 최단 기록: &e$record$"
  record-with-holders: '&a이 맵의 최단 기록: &e$record$&a by&b $holders$'
  newrecord: '&a$team$&a팀이 최단 플레이 시간 기록을 갱신했습니다: &6$record$'
  record-nobeddestroy: "&c침대를 파괴하지 않았으므로 기록이 저장되지 않습니다."
  teamwon: "Congratulations! Team $team$ won!"
  draw: "The game ends with a draw!"
  serverrestart: "Server restart in $sec$ second(s)!"
  gamestarting: "Game starting ..."
  gamestarted: "Game '$game$' has just started!"
  backtolobby: "Back to lobby in $sec$ second(s)!"
  spectator: "Spectator"
  spectate: "&aSpectate"
  teamchest: "Team chest"
  noturteamchest: "This chest isn't a chest of your team!"
  protectionleft: "Invulnerable for &c$length$&f second(s)!"
  protectionend: "You're now &cvulnerable&f again!"
  team-dead: "Team $team$ was destroyed!"
  no-friendlybreak: "&cCan't break block under team-member!"
  teamchestdestroy: "&cOne of your teamchest(s) has been destroyed!"
  title:
    map-builder: "Built by $builder$"
    win-title: "&6Congratulations!"
    win-subtitle: "$team$&6 won in &e$time$"
  shop:
    name: "Itemshop"
    fullstackpershift: "Multiply stacks per shift click"
    onestackpershift: "One stack per shift click"
  player:
    left: "Player $player$ has left the game!"
    died: "$player$ died!"
    killed: "$killer$ killed $player$!"
    kicked: "$player$ was kicked!"
    waskicked: "You were kicked from the game!"
  blocks:
    ownbeddestroy: "You can't destroy your own bed!"
    beddestroyed: "$player$ destroyed bed of team $team$!"
  specials:
    rescue-platform:
      left: "There are &c$time$&f second(s) left until you can use the next rescue platform!"
    arrow-blocker:
      start: "You are protected from being hit by arrows for &c$time$&f second(s)."
      end: "&cYour arrow protection is over"
      left: "There are &c$time$&f second(s) left until you can use the next arrowblocker!"
    trap:
      trapped: "&eSomeone went into a &ctrap&e of your team!"
    protection-wall:
      left: "There are &c$time$&f second(s) left until you can use the next protection wall!"
      not-usable-here: "You cannot use the protection wall here!"
    warp-powder:
      cancelled: "&cYour teleport was cancelled!"
      start: "You will be teleported in &c$time$&f second(s). Don't move!"
      cancel: "&4Cancel teleport"
      multiuse: "&cYou started a teleportation already!"
    tntsheep:
      no-target-found: "No target player was found!"
    tracker:
      no-target-found: "No target player was found!"
      target-found: "$player$ is $blocks$ block(s) away from you."
lobby:
  countdown: "Game will start in $sec$ second(s)!"
  countdowncancel: "More players needed. Countdown was cancelled!"
  cancelcountdown:
    not_enough_players: "More players needed. Countdown cancelled!"
    not_enough_teams: "More teams needed. Countdown cancelled!"
  cancelstart:
    not_enough_players: "More players are needed to start the game!"
    not_enough_teams: "More teams are needed to start the game!"
  chooseteam: "Choose a team"
  startgame: "Start game"
  reduce_countdown: "Reduce countdown"
  gamefull: "Game is full!"
  gamefullpremium: "The game is full of premium players already!"
  teamjoined: "You successfully joined the team $team$"
  leavegame: "Leave game"
  playerjoin: "Player $player$ joined the game!"
  kickedbyvip: "You were kicked by a vip player which has joined the full game!"
  moreplayersneeded: "$count$ more players needed."
  moreplayersneeded-one: "$count$ more player needed."
  moreteamsneeded: "A minimum of two players in two different teams is needed to start the game!"
sign:
  firstline: "&6[Bedwars]"
  players: "Players"
  gamestate:
    stopped: "&4Stopped!"
    waiting: "&aWaiting ..."
    running: "&9Running!"
    full: "Full!"
stats:
  header: "Bedwars Stats"
  kd: "K/D"
  statsnotfound: "Statistics of $player$ not found!"
  name: "Name"
  kills: "Kills"
  deaths: "Deaths"
  wins: "Wins"
  loses: "Loses"
  score: "Scores"
  destroyedBeds: "Destroyed Beds"
  games: "Games"
commands:
  addgame:
    name: "Add Game"
    desc: "Adds a new game"
  addteam:
    name: "Add Team"
    desc: "Adds a team to a specific game"
  join:
    name: "Join Game"
    desc: "Joins a specific game"
  leave:
    name: "Leave Game"
    desc: "Leave the current game"
  save:
    name: "Save Game"
    desc: "Saves a game (and map) to config file(s)"
  settarget:
    name: "Set target"
    desc: "Sets the location of a team's target block"
  setbed:
    name: "Set bed (synonym for settarget)"
    desc: "Sets the location of a team's bed block (Synonym for settarget)"
  setlobby:
    name: "Set lobby"
    desc: "Sets the location of the gamelobby"
  setregion:
    name: "Sets a region point"
    desc: "Sets a region point for the game"
  setspawn:
    name: "Sets a team spawn"
    desc: "Sets the spawn of the given team"
  setspawner:
    name: "Set spawner"
    desc: "Sets a spawner location of a specific ressource"
  start:
    name: "Start game"
    desc: "Starts a game"
  stop:
    name: "Stop game"
    desc: "Stops a game"
  help:
    name: "Show Help"
    desc: "Display information about the plugin and its commands"
  reload:
    name: "Reload"
    desc: "Reloads the configurations and translations"
  setmainlobby:
    name: "Set main lobby"
    desc: "Sets the main lobby of a game (is needed when mainlobby-enabled is set to true)"
  list:
    name: "List games"
    desc: "Lists all available games"
  regionname:
    name: "Set region name"
    desc: "Sets an individual region name (instead of world name)"
  removeteam:
    name: "Remove team"
    desc: "Removes a team from the game (only in stopped mode)"
  removegame:
    name: "Remove game"
    desc: "Removes a game and every configuration"
  clearspawner:
    name: "Clear spawners"
    desc: "Removes all spawners from the game. Saving needed."
  gametime:
    name: "Set game time"
    desc: "Sets the game time which should be used in the game-world"
  stats:
    name: "Statistics"
    desc: "Shows your statistics"
  setbuilder:
    name: "Sets builder of map"
    desc: "Sets the builder of the map which will be display in the title when game starts."
  setgameblock:
    name: "Set game block"
    desc: "Sets the game block type for this game which should be used instead of the 'game-block' configuration. Write 'DEFAULT' as type to use the type of config again"
  setautobalance:
    name: "Set autobalance"
    desc: "If 'global-autobalance' is set to 'false', with this command you can set team-autobalance per game to on or off!"
  setminplayers:
    name: "Set minimum players"
    desc: "Sets the amount of players needed to start the game"
  kick:
    name: "Kick player"
    desc: "Kicks a player from the current game!"
  addteamjoin:
    name: "Add team selection"
    desc: "Mark a creature which can be used to join a specific team!"
  addholo:
    name: "Add hologram location"
    desc: "A hologram statistic will be added at the current position!"
  removeholo:
    name: "Remove hologram location"
    desc: "When the command was executed, the player can right-click the hologram which should be removed."
    explain: "Perform a left click within 10 seconds on the hologram you want to remove."
  debugpaste:
    name: "Paste debug data"
    desc: "This will send some debug data to hastebin and returns a link you can share with the developers"
  itemspaste:
    name: "Paste your inventory to a file"
    desc: "This will return a link where you can see your current inventory being serialized as an example for your shop.yml"
