@echo off
echo ========================================
echo BedwarsRel 1.20 汉化版 JAR合并脚本
echo ========================================
echo.

echo [1/5] 清理旧文件...
if exist "temp" rmdir /s /q "temp"
if exist "BedwarsRel-1.20-1.3.6-汉化版-最终.jar" del "BedwarsRel-1.20-1.3.6-汉化版-最终.jar"

echo [2/5] 创建临时目录...
mkdir temp
cd temp

echo [3/5] 解压Common模块...
jar -xf "../common/target/BedwarsRel-Common-1.3.6.jar"
if %errorlevel% neq 0 (
    echo 错误: 无法解压Common模块JAR文件!
    echo 请确保已经构建了Common模块
    pause
    exit /b 1
)

echo [4/5] 解压v1_20_r1模块...
jar -xf "../v1_20_r1/target/BedwarsRel-v1_20_r1-1.3.6.jar"
if %errorlevel% neq 0 (
    echo 错误: 无法解压v1_20_r1模块JAR文件!
    echo 请确保已经构建了v1_20_r1模块
    pause
    exit /b 1
)

echo [5/5] 创建合并的JAR文件...
jar -cf "../BedwarsRel-1.20-1.3.6-汉化版-最终.jar" .
if %errorlevel% neq 0 (
    echo 错误: 无法创建合并的JAR文件!
    pause
    exit /b 1
)

cd ..
rmdir /s /q temp

echo.
echo ========================================
echo 合并完成!
echo ========================================
echo.

if exist "BedwarsRel-1.20-1.3.6-汉化版-最终.jar" (
    echo 生成的文件: BedwarsRel-1.20-1.3.6-汉化版-最终.jar
    echo 文件大小: 
    dir "BedwarsRel-1.20-1.3.6-汉化版-最终.jar" | findstr "BedwarsRel"
    echo.
    echo 文件内容验证:
    jar -tf "BedwarsRel-1.20-1.3.6-汉化版-最终.jar" | findstr "config.yml"
    jar -tf "BedwarsRel-1.20-1.3.6-汉化版-最终.jar" | findstr "shop.yml"
) else (
    echo 错误: 未找到生成的JAR文件!
)

echo.
echo 部署说明:
echo 1. 将 BedwarsRel-1.20-1.3.6-汉化版-最终.jar 复制到服务器的 plugins 目录
echo 2. 重启服务器
echo 3. 使用 /bw 命令测试功能
echo.
echo 汉化特色:
echo - 完整的中文界面和配置
echo - 默认语言设置为中文 (zh_CN)
echo - 商店界面完全汉化
echo - 配置文件注释中文化
echo - 1.20完全兼容
echo.
pause
