#配置版本 (請勿修改)
version: 6

#是否啟用
enabled: true

#商店
upgrade_shop:

  #商店標題
  title: "升級與陷阱"

  #裝飾欄
  frame:
  - "&8\u2b06 &7升級"
  - "&8\u2b07 &7陷阱"

  #陷阱
  trap:

    #物品名稱
    item: LEATHER

    #升級名稱
    name: "&e購買一個陷阱"

    lore:
    - "&7已購買的陷阱將加入右邊的隊列中."
    - ""
    - "&e點擊瀏覽！"

#陷阱商店
trap_shop:
  #標題
  title: "選擇陷阱"

  #返回
  back:
  - "&a返回"
  - "&7升級與陷阱"

#提示
message:

  #升級
  upgrade: "&7{player} &a購買了 &6{upgrade} {level}"

  #資源不足
  no_resource: "&c你沒有足夠的資源購買!"

#狀態
state:

  no_resource: "&c你沒有足夠的資源！"

  lock: "&e點擊購買！"

  unlock: "&a已解鎖！"

#觸發陷阱冷卻時間 (秒)
trap_cooldown: 60

#陷阱
trap:

  #陷阱列表
  trap_list:
    trap_1:
      lock:
      - "&c陷阱 #1: 沒有陷阱！"
      - "&7第一個敵人進入己方基地後"
      - "&7會觸發該陷阱！"
      - ""
      - "&7購買的陷阱會在此排列,具體"
      - "&7費用取決於已排列的陷阱數量。"
      - ""
      - "&7下一個陷阱:&b{cost} 鑽石"
      unlock:
      - "&a陷阱 #1: {trap}！"
      - ""
      - "&7第一個敵人進入己方基地後"
      - "&7會觸發該陷阱！"
      - ""
      - "&7購買者:{buyer}"
    trap_2:
      lock:
      - "&c陷阱 #2: 沒有陷阱！"
      - "&7第二個敵人進入己方基地後"
      - "&7會觸發該陷阱！"
      - ""
      - "&7購買的陷阱會在此排列,具體"
      - "&7費用取決於已排列的陷阱數量。"
      - ""
      - "&7下一個陷阱:&b{cost} 鑽石"
      unlock:
      - "&a陷阱 #2: {trap}！"
      - ""
      - "&7第二個敵人進入己方基地後"
      - "&7會觸發該陷阱！"
      - ""
      - "&7購買者:{buyer}"
    trap_3:
      lock:
      - "&c陷阱 #3: 沒有陷阱！"
      - "&7第三個敵人進入己方基地後"
      - "&7會觸發該陷阱！"
      - ""
      - "&7購買的陷阱會在此排列,具體"
      - "&7費用取決於已排列的陷阱數量。"
      - ""
      - "&7下一個陷阱:&b{cost} 鑽石"
      unlock:
      - "&a陷阱 #3: {trap}！"
      - ""
      - "&7第三個敵人進入己方基地後"
      - "&7會觸發該陷阱！"
      - ""
      - "&7購買者:{buyer}"

  #需要的資源
  #格式:
  #  level: 物品,數量
  #
  #經驗模式格式:
  #  level: XP,數量
  cost:
    level_1: DIAMOND,1
    level_2: DIAMOND,2
    level_3: DIAMOND,4

#升級
upgrade:

  #瘋狂礦工
  fast_dig:

    #是否啟用
    enabled: true

    #物品名稱
    item: GOLD_PICKAXE

    #升級名稱
    name: "瘋狂礦工"

    #等級一
    level_1:

      #需要的資源
      # cost: 物品,數量"
      #經驗模式
      # cost: XP,數量"
      cost: DIAMOND,4
      lore:
      - "&7己方所有成員獲得永久急迫效果。"
      - ""
      - "&71階:急迫I&7,&b4 鑽石"
      - "&72階:急迫II&7,&b6 鑽石"
      - ""
      - "{state}"

    #等級二
    level_2:
      cost: DIAMOND,6
      lore:
      - "&7己方所有成員獲得永久急迫效果。"
      - ""
      - "&a1階:急迫I&7,&b4 鑽石"
      - "&72階:急迫II&7,&b6 鑽石"
      - ""
      - "{state}"

    #滿級
    level_full:
      lore:
      - "&7己方所有成員獲得永久急迫效果。"
      - ""
      - "&a1階:急迫I&7,&b4 鑽石"
      - "&a2階:急迫II&7,&b6 鑽石"
      - ""
      - "{state}"

  #鋒利附魔
  sword_sharpness:
    enabled: true
    item: IRON_SWORD
    name: "鋒利附魔"
    level_1:
      cost: DIAMOND,8
      lore:
      - "&7己方所有成員的劍和斧將永久獲得鋒利附魔！"
      - ""
      - "&71階:鋒利I&7,&b8 鑽石"
      - "&72階:鋒利II&7,&b12 鑽石"
      - ""
      - "{state}"
    level_2:
      cost: DIAMOND,12
      lore:
      - "&7己方所有成員的劍和斧將永久獲得鋒利附魔！"
      - ""
      - "&a1階:鋒利I&7,&b8 鑽石"
      - "&72階:鋒利II&7,&b12 鑽石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7己方所有成員的劍和斧將永久獲得鋒利附魔！"
      - ""
      - "&a1階:鋒利I&7,&b8 鑽石"
      - "&a2階:鋒利II&7,&b12 鑽石"
      - ""
      - "{state}"

  #護甲強化
  armor_protection:
    enabled: true
    item: IRON_CHESTPLATE
    name: "護甲強化"
    level_1:
      cost: DIAMOND,5
      lore:
      - "&7己方所有成員的盔甲將獲得永久保護附魔！"
      - ""
      - "&71階:保護I&7,&b5 鑽石"
      - "&72階:保護II&7,&b10 鑽石"
      - "&73階:保護III&7,&b20 鑽石"
      - "&74階:保護IV&7,&b30 鑽石"
      - ""
      - "{state}"
    level_2:
      cost: DIAMOND,10
      lore:
      - "&7己方所有成員的盔甲將獲得永久保護附魔！"
      - ""
      - "&a1階:保護I&7,&b5 鑽石"
      - "&72階:保護II&7,&b10 鑽石"
      - "&73階:保護III&7,&b20 鑽石"
      - "&74階:保護IV&7,&b30 鑽石"
      - ""
      - "{state}"
    level_3:
      cost: DIAMOND,20
      lore:
      - "&7己方所有成員的盔甲將獲得永久保護附魔！"
      - ""
      - "&a1階:保護I&7,&b5 鑽石"
      - "&a2階:保護II&7,&b10 鑽石"
      - "&73階:保護III&7,&b20 鑽石"
      - "&74階:保護IV&7,&b30 鑽石"
      - ""
      - "{state}"
    level_4:
      cost: DIAMOND,30
      lore:
      - "&7己方所有成員的盔甲將獲得永久保護附魔！"
      - ""
      - "&a1階:保護I&7,&b5 鑽石"
      - "&a2階:保護II&7,&b10 鑽石"
      - "&a3階:保護III&7,&b20 鑽石"
      - "&74階:保護IV&7,&b30 鑽石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7己方所有成員的盔甲將獲得永久保護附魔！"
      - ""
      - "&a1階:保護I&7,&b5 鑽石"
      - "&a2階:保護II&7,&b10 鑽石"
      - "&a3階:保護III&7,&b20 鑽石"
      - "&a4階:保護IV&7,&b30 鑽石"
      - ""
      - "{state}"

  #治癒池
  heal:
    enabled: true
    item: BEACON
    name: "治癒池"
    trigger_range: 10
    level_1:
      cost: DIAMOND,3
      lore:
      - "&7基地附近的隊伍成員獲得生命恢復效果。"
      - ""
      - "&7花費:&b3 鑽石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7基地附近的隊伍成員獲得生命恢復效果。"
      - ""
      - "&7花費:&b3 鑽石"
      - ""
      - "{state}"

  #鐵鍛爐
  iron_forge:
    enabled: true
    item: FURNACE
    name: "鐵鍛爐"
    level_0:
      #資源生成速度 (每隔多少tick生成一個資源)
      #1 秒 = 20 tick
      #resources:
      #- 物品名稱,tick,擴散範圍
      resources:
      - IRON_INGOT,16,0.0
      - GOLD_INGOT,80,0.0
    level_1:
      cost: DIAMOND,4
      lore:
      - "&7提升自己島上生成資源的效率。"
      - ""
      - "&71階:+50%資源,&b4 鑽石"
      - "&72階:+100%資源,&b8 鑽石"
      - "&73階:生成綠寶石,&b12 鑽石"
      - "&74階:+200%資源,&b16鑽石"
      - ""
      - "{state}"
      resources:
      - IRON_INGOT,11,0.0
      - GOLD_INGOT,53,0.0
    level_2:
      cost: DIAMOND,8
      lore:
      - "&7提升自己島上生成資源的效率。"
      - ""
      - "&a1階:+50%資源,&b4 鑽石"
      - "&72階:+100%資源,&b8 鑽石"
      - "&73階:生成綠寶石,&b12 鑽石"
      - "&74階:+200%資源,&b16鑽石"
      - ""
      - "{state}"
      resources:
      - IRON_INGOT,8,0.0
      - GOLD_INGOT,40,0.0
    level_3:
      cost: DIAMOND,12
      lore:
      - "&7提升自己島上生成資源的效率。"
      - ""
      - "&a1階:+50%資源,&b4 鑽石"
      - "&a2階:+100%資源,&b8 鑽石"
      - "&73階:生成綠寶石,&b12 鑽石"
      - "&74階:+200%資源,&b16鑽石"
      - ""
      - "{state}"
      resources:
      - IRON_INGOT,8,0.0
      - GOLD_INGOT,40,0.0
      - EMERALD,1000,0.0
    level_4:
      cost: DIAMOND,16
      lore:
      - "&7提升自己島上生成資源的效率。"
      - ""
      - "&a1階:+50%資源,&b4 鑽石"
      - "&a2階:+100%資源,&b8 鑽石"
      - "&a3階:生成綠寶石,&b12 鑽石"
      - "&74階:+200%資源,&b16鑽石"
      - ""
      - "{state}"
      resources:
      - IRON_INGOT,5,0.0
      - GOLD_INGOT,27,0.0
      - EMERALD,1000,0.0
    level_full:
      lore:
      - "&7提升自己島上生成資源的效率。"
      - ""
      - "&a1階:+50%資源,&b4 鑽石"
      - "&a2階:+100%資源,&b8 鑽石"
      - "&a3階:生成綠寶石,&b12 鑽石"
      - "&a4階:+200%資源,&b16鑽石"
      - ""
      - "{state}"

  #這是個陷阱
  trap:
    enabled: true
    item: TRIPWIRE_HOOK
    name: "這是個陷阱"

    #觸發範圍
    trigger_range: 5

    #觸發後
    trigger:

      #大標題
      title: "&c觸發陷阱！"

      #小標題
      subtitle: "&7敵人已落入你的陷阱"

      #提示
      message: ""
    level_1:
      lore:
      - "&7敵人接近你隊伍基地時,造成失明與緩慢效果,持續5秒。"
      - ""
      - "&7花費:&b{cost} 鑽石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7敵人接近你隊伍基地時,造成失明與緩慢效果,持續5秒。"
      - ""
      - "&7花費:&b{cost} 鑽石"
      - ""
      - "{state}"

  #反擊陷阱
  counter_offensive_trap:
    enabled: true
    item: FEATHER
    name: "反擊陷阱"

    #觸發範圍
    trigger_range: 5

    #效果範圍
    effect_range: 10

    level_1:
      lore:
      - "&7敵人接近你隊伍基地時,基地附近的"
      - "&7隊伍成員獲得速度I與跳躍II效果,"
      - "&7持續10秒。"
      - ""
      - "&7花費:&b{cost} 鑽石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7敵人接近你隊伍基地時,基地附近的"
      - "&7隊伍成員獲得速度I與跳躍II效果,"
      - "&7持續10秒。"
      - ""
      - "&7花費:&b{cost} 鑽石"
      - ""
      - "{state}"

  #報警陷阱
  alarm_trap:
    enabled: true
    item: REDSTONE_TORCH_ON
    name: "報警陷阱"

    #觸發範圍
    trigger_range: 5

    #觸發後
    trigger:

      #大標題
      title: "&c觸發陷阱！"

      #小標題
      subtitle: "&f{player}&7({team_color}{team}&7) 已落入你的陷阱"

      #提示
      message: ""
    level_1:
      lore:
      - "&7顯示隱身的玩家"
      - "&7及其名稱與隊伍名。"
      - ""
      - "&7花費:&b{cost} 鑽石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7顯示隱身的玩家"
      - "&7及其名稱與隊伍名。"
      - ""
      - "&7花費:&b{cost} 鑽石"
      - ""
      - "{state}"

  #防禦系統
  defense:
    enabled: true
    item: IRON_PICKAXE
    name: "防禦系統"
    trigger_range: 5
    level_1:
      lore:
      - "&7敵人接近你隊伍基地時,造成挖掘疲勞效果"
      - ""
      - "&7花費:&b{cost} 鑽石"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7敵人接近你隊伍基地時,造成挖掘疲勞效果"
      - ""
      - "&7花費:&b{cost} 鑽石"
      - ""
      - "{state}"
