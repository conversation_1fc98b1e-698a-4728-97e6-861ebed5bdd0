#Config version (Don't edit!)
version: 6

#Enable team shop?
enabled: true

#Upgrade shop settings
upgrade_shop:

  #Shop GUI title
  title: "Upgrades & Traps"

  #Frame text
  frame:
  - "&8\u2b06 &7Purchasable"
  - "&8\u2b07 &7Traps"

  #Trap button
  trap:

    #Item name
    item: LEATHER

    #Item name
    name: "&eBuy a trap"

    #Item lore
    lore:
    - "&7Purchased traps will be"
    - "&7queued on the right."
    - ""
    - "&eClick to browse!"

#Trap shop settings
trap_shop:

  #Shop GUI title
  title: "Queue a trap"

  #Back button
  back:
  - "&aGo Back"
  - "&7To Upgrades & Traps"

message:

  upgrade: "&7{player} &apurchased &6{upgrade} {level}"

  no_resource: "&cYou don't have enough resource to unlock that upgrade!"

#Upgrade State
state:

  no_resource: "&cYou don't have enough resource!"

  lock: "&eClick to purchase!"

  unlock: "&aUnlock!"

#Trigger trap cooldown time (second)
trap_cooldown: 60

#Trap settings
trap:

  #Trap list
  trap_list:
    trap_1:
      lock:
      - "&cTrap #1: No Trap!"
      - ""
      - "&7The first enemy to walk"
      - "&7into your base will trigger"
      - "&7this trap!"
      - ""
      - "&7Purchasing a trap will"
      - "&7queue it here. Its cost"
      - "&7will scale based on the"
      - "&7number of traps queued."
      - ""
      - "&7Next trap: &b{cost} Diamond"
      unlock:
      - "&aTrap #1: {trap}!"
      - ""
      - "&7The first enemy to walk"
      - "&7into your base will trigger"
      - "&7this trap!"
      - ""
      - "&7Queued by: {buyer}"
    trap_2:
      lock:
      - "&cTrap #2: No Trap!"
      - ""
      - "&7The second enemy to walk"
      - "&7into your base will trigger"
      - "&7this trap!"
      - ""
      - "&7Purchasing a trap will"
      - "&7queue it here. Its cost"
      - "&7will scale based on the"
      - "&7number of traps queued."
      - ""
      - "&7Next trap: &b{cost} Diamond"
      unlock:
      - "&aTrap #2: {trap}!"
      - ""
      - "&7The second enemy to walk"
      - "&7into your base will trigger"
      - "&7this trap!"
      - ""
      - "&7Queued by: {buyer}"
    trap_3:
      lock:
      - "&cTrap #3: No Trap!"
      - ""
      - "&7The third enemy to walk"
      - "&7into your base will trigger"
      - "&7this trap!"
      - ""
      - "&7Purchasing a trap will"
      - "&7queue it here. Its cost"
      - "&7will scale based on the"
      - "&7number of traps queued."
      - ""
      - "&7Next trap: &b{cost} Diamond"
      unlock:
      - "&aTrap #3: {trap}!"
      - ""
      - "&7The third enemy to walk"
      - "&7into your base will trigger"
      - "&7this trap!"
      - ""
      - "&7Queued by: {buyer}"

  #Upgrade cost
  #Format:
  #  level: Item,Amount
  #
  #XP mode format (Dependent on BedwarsXP):
  #  level: XP,Amount
  cost:
    level_1: DIAMOND,1
    level_2: DIAMOND,2
    level_3: DIAMOND,4

#Upgrade settings
upgrade:

  #Maniac Miner
  fast_dig:

    enabled: true

    #Item name
    item: GOLD_PICKAXE

    #Upgrade name
    name: "Maniac Miner"

    #Level 1
    level_1:

      #Upgrade cost
      #Format:
      #  cost: Item,Amount
      #
      #XP mode format (Dependent on BedwarsXP):
      #  cost: XP,Amount
      cost: DIAMOND,4
      lore:
      - "&7All players on your team"
      - "&7permanently gain Haste."
      - ""
      - "&7Tier 1:Haste I&7, &b4 Diamonds"
      - "&7Tier 2:Haste II&7, &b6 Diamonds"
      - ""
      - "{state}"

    #Level 2
    level_2:
      cost: DIAMOND,6
      lore:
      - "&7All players on your team"
      - "&7permanently gain Haste."
      - ""
      - "&aTier 1:Haste I&7, &b4 Diamonds"
      - "&7Tier 2:Haste II&7, &b6 Diamonds"
      - ""
      - "{state}"

    #Full level
    level_full:
      lore:
      - "&7All players on your team"
      - "&7permanently gain Haste."
      - ""
      - "&aTier 1:Haste I&7, &b7 Diamonds"
      - "&aTier 2:Haste II&7, &b6 Diamonds"
      - ""
      - "{state}"

  #Sharpened Swords
  sword_sharpness:
    enabled: true
    item: IRON_SWORD
    name: "Sharpened Swords"
    level_1:
      cost: DIAMOND,8
      lore:
      - "&7Your team permanently gains"
      - "&7Sharpness I on all swords and axes!"
      - ""
      - "&7Tier 1:Sharpness I&7, &b8 Diamonds"
      - "&7Tier 2:Sharpness II&7, &b12 Diamonds"
      - ""
      - "{state}"
    level_2:
      cost: DIAMOND,12
      lore:
      - "&7Your team permanently gains"
      - "&7Sharpness II on all swords and axes!"
      - ""
      - "&aTier 1:Sharpness I&7, &b8 Diamonds"
      - "&7Tier 2:Sharpness II&7, &b12 Diamonds"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7Your team permanently gains"
      - "&7Sharpness II on all swords and axes!"
      - ""
      - "&aTier 1:Sharpness I&7, &b8 Diamonds"
      - "&aTier 2:Sharpness II&7, &b12 Diamonds"
      - ""
      - "{state}"

  #Reinforced Armor
  armor_protection:
    enabled: true
    item: IRON_CHESTPLATE
    name: "Reinforced Armor"
    level_1:
      cost: DIAMOND,5
      lore:
      - "&7Your team permanently gains"
      - "&7Protection on all armor pieces!"
      - ""
      - "&7Tier 1:Protection I&7, &b5 Diamonds"
      - "&7Tier 2:Protection II&7, &b10 Diamonds"
      - "&7Tier 3:Protection III&7, &b20 Diamonds"
      - "&7Tier 4:Protection IV&7, &b30 Diamonds"
      - ""
      - "{state}"
    level_2:
      cost: DIAMOND,10
      lore:
      - "&7Your team permanently gains"
      - "&7Protection on all armor pieces!"
      - ""
      - "&aTier 1:Protection I&7, &b5 Diamonds"
      - "&7Tier 2:Protection II&7, &b10 Diamonds"
      - "&7Tier 3:Protection III&7, &b20 Diamonds"
      - "&7Tier 4:Protection IV&7, &b30 Diamonds"
      - ""
      - "{state}"
    level_3:
      cost: DIAMOND,20
      lore:
      - "&7Your team permanently gains"
      - "&7Protection on all armor pieces!"
      - ""
      - "&aTier 1:Protection I&7, &b5 Diamonds"
      - "&aTier 2:Protection II&7, &b10 Diamonds"
      - "&7Tier 3:Protection III&7, &b20 Diamonds"
      - "&7Tier 4:Protection IV&7, &b30 Diamonds"
      - ""
      - "{state}"
    level_4:
      cost: DIAMOND,30
      lore:
      - "&7Your team permanently gains"
      - "&7Protection on all armor pieces!"
      - ""
      - "&aTier 1:Protection I&7, &b5 Diamonds"
      - "&aTier 2:Protection II&7, &b10 Diamonds"
      - "&aTier 3:Protection III&7, &b20 Diamonds"
      - "&7Tier 4:Protection IV&7, &b30 Diamonds"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7Your team permanently gains"
      - "&7Protection on all armor pieces!"
      - ""
      - "&aTier 1:Protection I&7, &b5 Diamonds"
      - "&aTier 2:Protection II&7, &b10 Diamonds"
      - "&aTier 3:Protection III&7, &b20 Diamonds"
      - "&aTier 4:Protection IV&7, &b30 Diamonds"
      - ""
      - "{state}"

  #Heal Pool
  heal:
    enabled: true
    item: BEACON
    name: "Heal Pool"

    #Trigger range
    trigger_range: 10
    level_1:
      cost: DIAMOND,3
      lore:
      - "&7Creates a Regeneration field"
      - "&7around your base!"
      - ""
      - "&7Cost: &b3 Diamonds"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7Creates a Regeneration field"
      - "&7around your base!"
      - ""
      - "&7Cost: &b3 Diamonds"
      - ""
      - "{state}"

  #Iron Forge
  iron_forge:
    enabled: true
    item: FURNACE
    name: "Iron Forge"
    level_0:
      #Resource spawn interval time
      #1 second = 20 tick
      #Format:
      #  resources:
      #  - <Resource item name>,<Interval time>,<Spread>
      resources:
      - IRON_INGOT,16,0.0
      - GOLD_INGOT,80,0.0
    level_1:
      cost: DIAMOND,4
      lore:
      - "&7Upgrade resource spawning on"
      - "&7your island."
      - ""
      - "&7Tier 1: +50% Resources, &b4 Diamonds"
      - "&7Tier 2: +100% Resources&7, &b8 Diamonds"
      - "&7Tier 3: Spawn emeralds&7, &b12 Diamonds"
      - "&7Tier 4: +200% Resources&7, &b16 Diamonds"
      - ""
      - "{state}"
      resources:
      - IRON_INGOT,11,0.0
      - GOLD_INGOT,53,0.0
    level_2:
      cost: DIAMOND,8
      lore:
      - "&7Upgrade resource spawning on"
      - "&7your island."
      - ""
      - "&aTier 1: +50% Resources, &b4 Diamonds"
      - "&7Tier 2: +100% Resources&7, &b8 Diamonds"
      - "&7Tier 3: Spawn emeralds&7, &b12 Diamonds"
      - "&7Tier 4: +200% Resources&7, &b16 Diamonds"
      - ""
      - "{state}"
      resources:
      - IRON_INGOT,8,0.0
      - GOLD_INGOT,40,0.0
    level_3:
      cost: DIAMOND,12
      lore:
      - "&7Upgrade resource spawning on"
      - "&7your island."
      - ""
      - "&aTier 1: +50% Resources, &b4 Diamonds"
      - "&aTier 2: +100% Resources&7, &b8 Diamonds"
      - "&7Tier 3: Spawn emeralds&7, &b12 Diamonds"
      - "&7Tier 4: +200% Resources&7, &b16 Diamonds"
      - ""
      - "{state}"
      resources:
      - IRON_INGOT,8,0.0
      - GOLD_INGOT,40,0.0
      - EMERALD,1000,0.0
    level_4:
      cost: DIAMOND,16
      lore:
      - "&7Upgrade resource spawning on"
      - "&7your island."
      - ""
      - "&aTier 1: +50% Resources, &b4 Diamonds"
      - "&aTier 2: +100% Resources&7, &b8 Diamonds"
      - "&aTier 3: Spawn emeralds&7, &b12 Diamonds"
      - "&7Tier 4: +200% Resources&7, &b16 Diamonds"
      - ""
      - "{state}"
      resources:
      - IRON_INGOT,5,0.0
      - GOLD_INGOT,27,0.0
      - EMERALD,1000,0.0
    level_full:
      lore:
      - "&7Upgrade resource spawning on"
      - "&7your island."
      - ""
      - "&aTier 1: +50% Resources, &b4 Diamonds"
      - "&aTier 2: +100% Resources&7, &b8 Diamonds"
      - "&aTier 3: Spawn emeralds&7, &b12 Diamonds"
      - "&aTier 4: +200% Resources&7, &b16 Diamonds"
      - ""
      - "{state}"

  #It's a trap!
  trap:
    enabled: true
    item: TRIPWIRE_HOOK
    name: "It's a trap!"

    #Trigger range
    trigger_range: 5

    #Triggered
    trigger:
      title: "&c&lTRIGGER TRAP!"
      subtitle: "&7The enemy has triggered your trap!"
      message: ""
    level_1:
      lore:
      - "&7Inflicts Blindness and Slowness"
      - "&7for 5 seconds."
      - ""
      - "&7Cost: &b{cost} Diamonds"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7Inflicts Blindness and Slowness"
      - "&7for 5 seconds."
      - ""
      - "&7Cost: &b{cost} Diamonds"
      - ""
      - "{state}"

  #Counter-Offensive Trap
  counter_offensive_trap:
    enabled: true
    item: FEATHER
    name: "Counter-Offensive Trap"
    trigger_range: 5
    effect_range: 10
    level_1:
      lore:
      - "&7Grants Speed I and Jump Boost II"
      - "&7for 10 seconds to allied players"
      - "&7near your base."
      - ""
      - "&7Cost: &b{cost} Diamonds"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7Grants Speed I and Jump Boost II"
      - "&7for 10 seconds to allied players"
      - "&7near your base."
      - ""
      - "&7Cost: &b{cost} Diamonds"
      - ""
      - "{state}"

  #Alarm Trap
  alarm_trap:
    enabled: true
    item: REDSTONE_TORCH_ON
    name: "Alarm Trap"
    trigger_range: 5

    #Triggered
    trigger:
      title: "&cALARM TRAP"
      subtitle: "&f{player}&7({team_color}{team}&7) has fallen into the trap"
      message: ""
    level_1:
      lore:
      - "&7Reveals invisible players as"
      - "&7well as their name and team."
      - ""
      - "&7Cost: &b{cost} Diamonds"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7Reveals invisible players as"
      - "&7well as their name and team."
      - ""
      - "&7Cost: &b{cost} Diamonds"
      - ""
      - "{state}"

  #Defense
  defense:
    enabled: true
    item: IRON_PICKAXE
    name: "Defense"

    #Trigger range
    trigger_range: 5
    level_1:
      lore:
      - "&7Inflict Mining Fatifue for 10"
      - "&7seconds."
      - ""
      - "&7Cost: &b{cost} Diamonds"
      - ""
      - "{state}"
    level_full:
      lore:
      - "&7Inflict Mining Fatifue for 10"
      - "&7seconds."
      - ""
      - "&7Cost: &b{cost} Diamonds"
      - ""
      - "{state}"
