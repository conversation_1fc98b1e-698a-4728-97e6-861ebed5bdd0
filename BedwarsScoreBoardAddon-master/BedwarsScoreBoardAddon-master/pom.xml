<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  
  <modelVersion>4.0.0</modelVersion>
  
  <groupId>me.ram</groupId>
  <artifactId>BedwarsScoreBoardAddon</artifactId>
  <version>2.13.1</version>
  <packaging>jar</packaging>
  
  <name>BedwarsScoreBoardAddon</name>
  <description>BedwarsRel扩展插件，增强床战游戏体验</description>
  
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <versionstring>2.13.1</versionstring>
  </properties>
  
  <repositories>
    <repository>
      <id>spigot-repo</id>
      <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
    </repository>
    <repository>
      <id>placeholderapi</id>
      <url>https://repo.extendedclip.com/content/repositories/placeholderapi/</url>
    </repository>
    <repository>
      <id>citizens-repo</id>
      <url>https://maven.citizensnpcs.co/repo</url>
    </repository>
    <repository>
      <id>dmulloy2-repo</id>
      <url>https://repo.dmulloy2.net/repository/public/</url>
    </repository>
    <repository>
      <id>codemc-snapshots</id>
      <url>https://repo.codemc.org/repository/maven-snapshots/</url>
    </repository>
  </repositories>
  
  <dependencies>
    <!-- Spigot API -->
    <dependency>
      <groupId>org.spigotmc</groupId>
      <artifactId>spigot-api</artifactId>
      <version>1.20.1-R0.1-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    
    <!-- BedwarsRel -->
    <dependency>
      <groupId>io.github.bedwarsrel</groupId>
      <artifactId>BedwarsRel-1.20-Chinese</artifactId>
      <version>1.3.6</version>
      <scope>system</scope>
      <systemPath>${pom.basedir}/../../BedwarsRel-master/BedwarsRel-master/target/BedwarsRel-1.20-Chinese-1.3.6.jar</systemPath>
    </dependency>
    
    <!-- BedwarsXP -->
    <dependency>
      <groupId>ldcr</groupId>
      <artifactId>BedwarsXP</artifactId>
      <version>1.0</version>
      <scope>system</scope>
      <systemPath>${pom.basedir}/../../BedwarsXP-master/BedwarsXP-master/target/BedwarsXP-1.0.jar</systemPath>
    </dependency>
    
    <!-- ProtocolLib -->
    <dependency>
      <groupId>com.comphenix.protocol</groupId>
      <artifactId>ProtocolLib</artifactId>
      <version>5.1.0</version>
      <scope>provided</scope>
    </dependency>
    
    <!-- Citizens -->
    <dependency>
      <groupId>net.citizensnpcs</groupId>
      <artifactId>citizens-main</artifactId>
      <version>2.0.32-SNAPSHOT</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    
    <!-- PlaceholderAPI -->
    <dependency>
      <groupId>me.clip</groupId>
      <artifactId>placeholderapi</artifactId>
      <version>2.11.5</version>
      <scope>provided</scope>
    </dependency>
    
    <!-- Lombok -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.30</version>
      <scope>provided</scope>
    </dependency>
    
    <!-- bStats -->
    <dependency>
      <groupId>org.bstats</groupId>
      <artifactId>bstats-bukkit</artifactId>
      <version>3.0.2</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  
  <build>
    <finalName>BedwarsScoreBoardAddon-${project.version}</finalName>
    <defaultGoal>clean package</defaultGoal>
    
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    
    <plugins>
      <!-- Maven Compiler Plugin -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <configuration>
          <source>17</source>
          <target>17</target>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      
      <!-- Maven Resources Plugin -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.3.1</version>
        <configuration>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      
      <!-- Maven Shade Plugin -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.5.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <createDependencyReducedPom>false</createDependencyReducedPom>
              <shadedArtifactAttached>false</shadedArtifactAttached>
              
              <!-- 包含bStats -->
              <artifactSet>
                <includes>
                  <include>org.bstats:bstats-bukkit</include>
                </includes>
              </artifactSet>
              
              <!-- 重定位bStats包 -->
              <relocations>
                <relocation>
                  <pattern>org.bstats</pattern>
                  <shadedPattern>me.ram.bedwarsscoreboardaddon.bstats</shadedPattern>
                </relocation>
              </relocations>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
