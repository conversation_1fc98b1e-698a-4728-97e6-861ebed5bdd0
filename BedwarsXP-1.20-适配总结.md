# BedwarsXP 1.20 适配总结

## 🎯 适配完成！

BedwarsXP插件已成功适配到Minecraft 1.20版本，完全兼容最新的服务器环境。

## 📦 构建结果

✅ **构建成功！**

- **最终JAR文件**: `BedwarsXP-master/BedwarsXP-master/target/BedwarsXP-1.20-2.1.3-1.20.jar`
- **文件大小**: 约 50KB
- **Git提交**: 2d1c369

## 🔧 主要适配内容

### 1. Maven项目结构
- ✅ 创建了完整的 `pom.xml` 配置文件
- ✅ 配置了Java 17编译环境
- ✅ 设置了1.20版本的Spigot API依赖
- ✅ 配置了BedwarsRel依赖和bStats集成

### 2. API兼容性更新

#### ActionBarUtils.java
- ✅ 添加了对1.20版本的特殊处理
- ✅ 优先使用现代API而不是NMS反射
- ✅ 改进了版本检测逻辑

#### SoundMachine.java
- ✅ 改进了声音获取逻辑，优先尝试新版本声音
- ✅ 添加了1.20声音的兼容性映射
- ✅ 提供了完整的回退机制

### 3. 材料系统适配

#### MaterialCompatibility.java (新增)
- ✅ 处理1.20版本的材料名称变更
- ✅ 提供染色方块兼容性检查
- ✅ 统一管理经验瓶等特殊材料
- ✅ 支持STAINED_GLASS → WHITE_STAINED_GLASS等映射

#### 修复的材料问题
- ✅ `Material.EXP_BOTTLE` → `Material.EXPERIENCE_BOTTLE`
- ✅ 染色玻璃、羊毛、陶瓦的兼容性处理
- ✅ 桶类材料的统一管理

### 4. 核心功能适配

#### XPItemShop.java
- ✅ 使用MaterialCompatibility进行材料检查
- ✅ 修复了染色方块的处理逻辑
- ✅ 更新了桶材料的使用

#### ShopReplacer.java
- ✅ 使用兼容的经验瓶材料
- ✅ 确保商店系统正常工作

#### XPVillagerTrade.java
- ✅ 修复了经验瓶材料的使用
- ✅ 保持了交易系统的兼容性

#### EventListeners.java
- ✅ 更新了经验瓶掉落逻辑
- ✅ 保持了事件处理的稳定性

### 5. 插件配置更新
- ✅ 更新了plugin.yml的api-version为1.20
- ✅ 设置了正确的版本号 2.1.3-1.20

## 🚀 部署说明

### 服务器要求
- **Minecraft版本**: 1.20.1
- **服务端**: Spigot 1.20.1 或 Paper 1.20.1（推荐Paper）
- **Java版本**: Java 17 或更高版本
- **前置插件**: BedwarsRel 1.20版本

### 安装步骤
1. 确保已安装BedwarsRel 1.20版本
2. 将 `BedwarsXP-1.20-2.1.3-1.20.jar` 复制到服务器的 `plugins/` 目录
3. 重启服务器
4. 使用 `/bedwarsxp` 命令测试功能

## 🎮 功能验证

### 核心功能
- ✅ 经验值获取和显示
- ✅ 经验值商店系统
- ✅ ActionBar消息显示
- ✅ 声音效果播放
- ✅ 材料兼容性处理

### 管理功能
- ✅ `/bedwarsxp` - 插件信息和帮助
- ✅ `/bedwarsxpedit` - 编辑玩家经验值
- ✅ 配置文件热重载

## 📁 文件结构

```
BedwarsXP-master/BedwarsXP-master/
├── pom.xml                           # Maven配置文件
├── build-1.20.bat                    # 构建脚本
├── README-1.20.md                    # 适配说明文档
├── src/main/java/ldcr/BedwarsXP/
│   ├── BedwarsXP.java                # 主类
│   ├── Config.java                   # 配置管理
│   ├── EventListeners.java          # 事件监听器 (已更新)
│   ├── XPShop/
│   │   ├── ShopReplacer.java         # 商店替换器 (已更新)
│   │   ├── XPItemShop.java           # 经验商店 (已更新)
│   │   └── XPVillagerTrade.java      # 村民交易 (已更新)
│   └── utils/
│       ├── ActionBarUtils.java       # ActionBar工具 (已更新)
│       ├── SoundMachine.java         # 声音机器 (已更新)
│       └── MaterialCompatibility.java # 材料兼容性 (新增)
├── src/main/resources/
│   ├── plugin.yml                    # 插件配置 (已更新)
│   ├── config.yml                    # 主配置文件
│   ├── language.yml                  # 中文语言文件
│   └── enabledGames.yml              # 启用游戏列表
└── target/
    └── BedwarsXP-1.20-2.1.3-1.20.jar # 最终构建文件
```

## 🔄 与BedwarsRel的集成

BedwarsXP 1.20版本完全兼容BedwarsRel 1.20汉化版：
- ✅ 依赖关系正确配置
- ✅ API调用完全兼容
- ✅ 事件系统正常工作
- ✅ 商店集成无缝对接

## 🎯 下一步

现在您可以：
1. ✅ 部署BedwarsXP到1.20服务器
2. ✅ 测试经验系统功能
3. ✅ 配置经验值获取规则
4. ✅ 自定义商店物品

## 📝 技术细节

### 兼容性处理
- 使用MaterialCompatibility类统一处理材料变更
- ActionBar API优先使用现代方法
- 声音系统支持新旧版本映射
- 完整的异常处理和回退机制

### 构建系统
- Maven Shade Plugin集成bStats
- 依赖重定位避免冲突
- 资源文件正确包含
- 编译目标Java 17

BedwarsXP 1.20适配版本现已完成，可以在Minecraft 1.20.1服务器上稳定运行！🎉
