package ldcr.BedwarsXP.utils;

import org.bukkit.Material;

/**
 * 材料兼容性工具类，处理1.20版本的材料变更
 */
public class MaterialCompatibility {
    
    /**
     * 检查材料是否为染色玻璃（兼容1.20）
     */
    public static boolean isStainedGlass(Material material) {
        if (material == null) {
            return false;
        }
        
        String name = material.name();
        return name.endsWith("_STAINED_GLASS") || name.equals("STAINED_GLASS");
    }
    
    /**
     * 检查材料是否为羊毛（兼容1.20）
     */
    public static boolean isWool(Material material) {
        if (material == null) {
            return false;
        }
        
        String name = material.name();
        return name.endsWith("_WOOL") || name.equals("WOOL");
    }
    
    /**
     * 检查材料是否为染色粘土/陶瓦（兼容1.20）
     */
    public static boolean isStainedClay(Material material) {
        if (material == null) {
            return false;
        }
        
        String name = material.name();
        return name.endsWith("_TERRACOTTA") || name.equals("STAINED_CLAY") || name.equals("HARD_CLAY");
    }
    
    /**
     * 检查材料是否为可染色的方块
     */
    public static boolean isColorableMaterial(Material material) {
        return isStainedGlass(material) || isWool(material) || isStainedClay(material);
    }
    
    /**
     * 获取经验瓶材料（兼容1.20）
     */
    public static Material getExpBottleMaterial() {
        try {
            // 尝试通过字符串获取经验瓶材料，避免直接引用
            Material expBottle = Material.getMaterial("EXPERIENCE_BOTTLE");
            if (expBottle != null) {
                return expBottle;
            }
        } catch (Exception e) {
            // 忽略异常，继续尝试其他方式
        }

        try {
            // 尝试获取旧版本的经验瓶
            Material expBottle = Material.getMaterial("EXP_BOTTLE");
            if (expBottle != null) {
                return expBottle;
            }
        } catch (Exception e) {
            // 忽略异常
        }

        // 最后的回退
        return Material.GLASS_BOTTLE;
    }
    
    /**
     * 获取桶材料（兼容1.20）
     */
    public static Material getBucketMaterial() {
        Material bucket = Material.getMaterial("BUCKET");
        return bucket != null ? bucket : Material.getMaterial("WATER_BUCKET");
    }

    /**
     * 获取岩浆桶材料（兼容1.20）
     */
    public static Material getLavaBucketMaterial() {
        Material lavaBucket = Material.getMaterial("LAVA_BUCKET");
        return lavaBucket != null ? lavaBucket : Material.getMaterial("BUCKET");
    }

    /**
     * 获取白色染色玻璃材料（兼容1.20）
     */
    public static Material getWhiteStainedGlassMaterial() {
        try {
            Material whiteGlass = Material.getMaterial("WHITE_STAINED_GLASS");
            if (whiteGlass != null) {
                return whiteGlass;
            }
        } catch (Exception e) {
            // 忽略异常
        }

        try {
            Material stainedGlass = Material.getMaterial("STAINED_GLASS");
            if (stainedGlass != null) {
                return stainedGlass;
            }
        } catch (Exception e) {
            // 忽略异常
        }

        // 最后的回退
        return Material.GLASS;
    }
    
    /**
     * 获取兼容的材料
     */
    public static Material getCompatibleMaterial(String materialName) {
        if (materialName == null || materialName.isEmpty()) {
            return Material.STONE;
        }
        
        try {
            // 首先尝试直接获取
            Material material = Material.getMaterial(materialName.toUpperCase());
            if (material != null) {
                return material;
            }
            
            // 处理一些常见的材料映射
            switch (materialName.toUpperCase()) {
                case "STAINED_GLASS":
                    Material whiteGlass = Material.getMaterial("WHITE_STAINED_GLASS");
                    return whiteGlass != null ? whiteGlass : Material.GLASS;
                case "STAINED_CLAY":
                case "HARD_CLAY":
                    Material whiteTerracotta = Material.getMaterial("WHITE_TERRACOTTA");
                    return whiteTerracotta != null ? whiteTerracotta : Material.getMaterial("CLAY");
                case "EXP_BOTTLE":
                case "EXPERIENCE_BOTTLE":
                    return getExpBottleMaterial();
                case "WOOL":
                    Material whiteWool = Material.getMaterial("WHITE_WOOL");
                    return whiteWool != null ? whiteWool : Material.getMaterial("WOOL");
                default:
                    return Material.STONE;
            }
        } catch (Exception e) {
            return Material.STONE;
        }
    }
}
