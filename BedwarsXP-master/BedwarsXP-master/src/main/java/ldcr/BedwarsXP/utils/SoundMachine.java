package ldcr.BedwarsXP.utils;

import org.bukkit.Sound;

public class SoundMachine {
    public static Sound get(String v18, String v19) {
        try {
            // 首先尝试新版本的声音名称（适用于1.20）
            return Sound.valueOf(v19);
        } catch (IllegalArgumentException e1) {
            try {
                // 如果新版本失败，尝试旧版本的声音名称
                return Sound.valueOf(v18);
            } catch (IllegalArgumentException e2) {
                // 如果都失败了，尝试一些常见的1.20声音映射
                return getCompatibleSound(v18, v19);
            }
        }
    }

    /**
     * 获取兼容的声音，处理1.20版本的声音变更
     */
    private static Sound getCompatibleSound(String v18, String v19) {
        // 处理一些常见的声音映射
        try {
            switch (v19.toUpperCase()) {
                case "ENTITY_ITEM_PICKUP":
                    return Sound.ENTITY_ITEM_PICKUP;
                case "BLOCK_NOTE_BLOCK_PLING":
                    return Sound.BLOCK_NOTE_BLOCK_PLING;
                case "ENTITY_PLAYER_LEVELUP":
                    return Sound.ENTITY_PLAYER_LEVELUP;
                case "UI_BUTTON_CLICK":
                    return Sound.UI_BUTTON_CLICK;
                default:
                    // 如果没有找到映射，返回一个默认声音
                    return Sound.BLOCK_NOTE_BLOCK_PLING;
            }
        } catch (Exception e) {
            // 最后的回退
            return Sound.BLOCK_NOTE_BLOCK_PLING;
        }
    }

}
