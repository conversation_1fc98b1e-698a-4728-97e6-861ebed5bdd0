@echo off
echo ========================================
echo BedwarsXP 1.20 适配版构建脚本
echo ========================================
echo.

echo [1/4] 检查前置条件...
if not exist "../../target/BedwarsRel-1.20-1.3.6-汉化版.jar" (
    echo 错误: 未找到BedwarsRel依赖文件!
    echo 请先构建BedwarsRel项目
    pause
    exit /b 1
)

echo [2/4] 清理旧的构建文件...
if exist "target" rmdir /s /q "target"

echo [3/4] 开始Maven构建...
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo 错误: Maven构建失败!
    pause
    exit /b 1
)

echo [4/4] 构建完成!
echo.

echo ========================================
echo 构建成功完成!
echo ========================================
echo.

if exist "target\BedwarsXP-1.20-2.1.3-1.20.jar" (
    echo 生成的文件: target\BedwarsXP-1.20-2.1.3-1.20.jar
    echo 文件大小: 
    dir "target\BedwarsXP-1.20-2.1.3-1.20.jar" | findstr "BedwarsXP"
) else (
    echo 警告: 未找到生成的JAR文件
)

echo.
echo 部署说明:
echo 1. 将JAR文件复制到服务器的 plugins 目录
echo 2. 确保已安装BedwarsRel 1.20版本
echo 3. 重启服务器
echo 4. 使用 /bedwarsxp 命令测试功能
echo.
echo 1.20适配特色:
echo - 完全兼容Minecraft 1.20.1
echo - 更新了ActionBar API
echo - 修复了材料兼容性问题
echo - 优化了声音系统
echo - 支持新版本的经验系统
echo.
pause
