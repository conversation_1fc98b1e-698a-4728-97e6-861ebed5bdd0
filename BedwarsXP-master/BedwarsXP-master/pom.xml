<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  
  <modelVersion>4.0.0</modelVersion>
  
  <groupId>ldcr</groupId>
  <artifactId>BedwarsXP-1.20</artifactId>
  <version>2.1.3-1.20</version>
  <packaging>jar</packaging>
  
  <name>BedwarsXP 1.20 适配版</name>
  <description>BedwarsXP插件的Minecraft 1.20适配版本</description>
  
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
  </properties>
  
  <repositories>
    <repository>
      <id>spigot-repo</id>
      <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
    </repository>
    <repository>
      <id>codemc-repo</id>
      <url>https://repo.codemc.org/repository/maven-public/</url>
    </repository>
  </repositories>
  
  <dependencies>
    <!-- Spigot API -->
    <dependency>
      <groupId>org.spigotmc</groupId>
      <artifactId>spigot-api</artifactId>
      <version>1.20.1-R0.1-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    
    <!-- BedwarsRel 依赖 -->
    <dependency>
      <groupId>io.github.bedwarsrel</groupId>
      <artifactId>BedwarsRel-1.20-Chinese</artifactId>
      <version>1.3.6</version>
      <scope>system</scope>
      <systemPath>${pom.basedir}/../../target/BedwarsRel-1.20-1.3.6-汉化版.jar</systemPath>
    </dependency>
    
    <!-- Lombok -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.30</version>
      <scope>provided</scope>
    </dependency>
    
    <!-- bStats Metrics -->
    <dependency>
      <groupId>org.bstats</groupId>
      <artifactId>bstats-bukkit</artifactId>
      <version>3.0.2</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  
  <build>
    <finalName>BedwarsXP-1.20-${project.version}</finalName>
    
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    
    <plugins>
      <!-- Maven Compiler Plugin -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <configuration>
          <source>17</source>
          <target>17</target>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      
      <!-- Maven Resources Plugin -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.3.1</version>
        <configuration>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      
      <!-- Maven Shade Plugin -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.5.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <createDependencyReducedPom>false</createDependencyReducedPom>
              <shadedArtifactAttached>false</shadedArtifactAttached>
              
              <!-- 包含依赖 -->
              <artifactSet>
                <includes>
                  <include>org.bstats:bstats-bukkit</include>
                  <include>org.bstats:bstats-base</include>
                </includes>
              </artifactSet>
              
              <!-- 重定位包名避免冲突 -->
              <relocations>
                <relocation>
                  <pattern>org.bstats</pattern>
                  <shadedPattern>ldcr.BedwarsXP.libs.bstats</shadedPattern>
                </relocation>
              </relocations>
              
              <!-- 过滤不需要的文件 -->
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
