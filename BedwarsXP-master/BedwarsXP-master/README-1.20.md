# BedwarsXP 1.20 适配版

这是BedwarsXP插件的Minecraft 1.20适配版本，完全兼容最新的Minecraft 1.20.1服务器。

## 🎯 适配内容

### ✅ 已完成的适配

1. **Maven项目结构**
   - 创建了完整的pom.xml配置文件
   - 配置了1.20版本的依赖项
   - 设置了Java 17编译环境

2. **API兼容性更新**
   - 更新了ActionBarUtils以支持1.20版本
   - 优化了SoundMachine的声音映射
   - 修复了材料兼容性问题

3. **材料系统适配**
   - 创建了MaterialCompatibility工具类
   - 处理了1.20版本的材料名称变更
   - 修复了染色方块的兼容性

4. **插件配置更新**
   - 更新了plugin.yml的api-version
   - 设置了正确的版本号

### 🔧 主要变更

#### ActionBarUtils.java
- 添加了对1.20版本的特殊处理
- 优先使用现代API而不是NMS反射

#### SoundMachine.java
- 改进了声音获取逻辑
- 添加了1.20声音的兼容性映射
- 提供了回退机制

#### MaterialCompatibility.java (新增)
- 处理材料名称变更（如STAINED_GLASS → WHITE_STAINED_GLASS）
- 提供材料兼容性检查方法
- 统一管理经验瓶等特殊材料

#### XPItemShop.java
- 使用MaterialCompatibility进行材料检查
- 修复了染色方块的处理逻辑

#### ShopReplacer.java
- 使用兼容的经验瓶材料
- 确保商店系统正常工作

## 🚀 构建说明

### 前置要求
- Java 17 或更高版本
- Maven 3.6 或更高版本
- 已构建的BedwarsRel 1.20版本

### 构建步骤

#### 方法一：使用构建脚本（推荐）
```bash
# Windows
build-1.20.bat

# Linux/Mac
chmod +x build-1.20.sh
./build-1.20.sh
```

#### 方法二：手动构建
```bash
mvn clean package -DskipTests
```

### 构建输出
- `target/BedwarsXP-1.20-2.1.3-1.20.jar` - 主要插件文件

## 📦 部署说明

### 服务器要求
- **Minecraft版本**: 1.20.1
- **服务端**: Spigot 1.20.1 或 Paper 1.20.1（推荐Paper）
- **Java版本**: Java 17 或更高版本
- **前置插件**: BedwarsRel 1.20版本

### 安装步骤
1. 确保已安装BedwarsRel 1.20版本
2. 将 `BedwarsXP-1.20-2.1.3-1.20.jar` 复制到服务器的 `plugins/` 目录
3. 重启服务器
4. 使用 `/bedwarsxp` 命令测试功能

## 🎮 功能特色

### 经验系统
- 玩家在床战游戏中可以获得经验值
- 经验值可以用于购买特殊物品
- 支持经验值的显示和管理

### 商店系统
- 集成到BedwarsRel的商店系统
- 支持经验值交换物品
- 完全兼容1.20版本的材料系统

### 管理功能
- `/bedwarsxp` - 查看插件信息和帮助
- `/bedwarsxpedit` - 编辑玩家经验值（需要权限）
- 支持配置文件热重载

## 🔧 配置说明

### 主要配置文件
- `config.yml` - 主要配置文件
- `language.yml` - 语言文件（中文）
- `enabledGames.yml` - 启用的游戏列表

### 权限节点
- `bedwarsxp.admin` - 管理员权限（默认：op）

## 🐛 已知问题

目前没有已知的严重问题。如果遇到问题，请检查：
1. 服务器版本是否为1.20.1
2. BedwarsRel是否正确安装
3. Java版本是否为17或更高

## 📝 更新日志

### v2.1.3-1.20
- 完全适配Minecraft 1.20.1
- 更新ActionBar API支持
- 修复材料兼容性问题
- 优化声音系统
- 添加MaterialCompatibility工具类
- 改进构建系统

## 🤝 贡献

如果您发现问题或有改进建议，请：
1. 检查现有的issue
2. 创建详细的bug报告
3. 提供复现步骤和环境信息

## 📄 许可证

本项目基于原BedwarsXP项目进行1.20适配，遵循原项目的许可证条款。
