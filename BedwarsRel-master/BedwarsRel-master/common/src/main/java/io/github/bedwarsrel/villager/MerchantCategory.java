package io.github.bedwarsrel.villager;

import io.github.bedwarsrel.utils.MaterialCompatibility;

import io.github.bedwarsrel.BedwarsRel;
import io.github.bedwarsrel.game.Game;
import io.github.bedwarsrel.utils.ChatWriter;
import io.github.bedwarsrel.utils.Utils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

public class MerchantCategory {

  private Material item = null;
  private List<String> lores = null;
  private String name = null;
  private ArrayList<VillagerTrade> offers = null;
  private int order = 0;
  private String permission = null;

  public MerchantCategory(String name, Material item) {
    this(name, item, new ArrayList<VillagerTrade>(), new ArrayList<String>(), 0, "bw.base");
  }

  public MerchantCategory(String name, Material item, ArrayList<VillagerTrade> offers,
      List<String> lores, int order, String permission) {
    this.name = name;
    this.item = item;
    this.offers = offers;
    this.lores = lores;
    this.order = order;
    this.permission = permission;
  }

  @SuppressWarnings({"unchecked", "deprecation"})
  public static HashMap<Material, MerchantCategory> loadCategories(FileConfiguration cfg) {
    if (cfg.getConfigurationSection("shop") == null) {
      return new HashMap<Material, MerchantCategory>();
    }

    HashMap<Material, MerchantCategory> mc = new HashMap<Material, MerchantCategory>();

    ConfigurationSection section = cfg.getConfigurationSection("shop");

    for (String cat : section.getKeys(false)) {
      String catName =
          ChatColor.translateAlternateColorCodes('&', section.getString(cat + ".name"));
      Material catItem = null;
      List<String> lores = new ArrayList<String>();
      String item = section.get(cat + ".item").toString();
      String permission = "bw.base";
      int order = 0;

      if (!Utils.isNumber(item)) {
        catItem = Material.getMaterial(section.getString(cat + ".item"));
      } else {
        // 1.20版本不再支持通过ID获取材料，尝试转换
        int materialId = section.getInt(cat + ".item");
        catItem = MaterialCompatibility.getCompatibleMaterial(String.valueOf(materialId));
      }

      if (section.contains(cat + ".lore")) {
        for (Object lore : section.getList(cat + ".lore")) {
          lores.add(ChatColor.translateAlternateColorCodes('&', lore.toString()));
        }
      }

      if (section.contains(cat + ".order") && section.isInt(cat + ".order")) {
        order = section.getInt(cat + ".order");
      }

      if (section.contains(cat + ".permission")) {
        permission = section.getString(cat + ".permission", "bw.base");
      }

      ArrayList<VillagerTrade> offers = new ArrayList<VillagerTrade>();

      for (Object offer : section.getList(cat + ".offers")) {
        if (offer instanceof String) {
          if (offer.toString().equalsIgnoreCase("empty")
              || offer.toString().equalsIgnoreCase("null")
              || offer.toString().equalsIgnoreCase("e")) {
            VillagerTrade trade =
                new VillagerTrade(new ItemStack(Material.AIR, 1), new ItemStack(Material.AIR, 1));
            offers.add(trade);
          }

          continue;
        }

        HashMap<String, List<Map<String, Object>>> offerSection =
            (HashMap<String, List<Map<String, Object>>>) offer;

        if (!offerSection.containsKey("price") || !offerSection.containsKey("reward")) {
          continue;
        }

        ItemStack item1 = null;

        try {
          Map<String, Object> priceData = offerSection.get("price").get(0);
          item1 = deserializeItemStackSafely(priceData, "price item 1");
          if (item1 != null) {
            item1 = setResourceName(item1);
          }
        } catch (Exception e) {
          BedwarsRel.getInstance().getLogger().warning("解析商店价格物品1失败 (类别: " + section.getString(cat + ".name") + "): " + e.getMessage());
        }

        ItemStack item2 = null;
        if (offerSection.get("price").size() == 2) {
          try {
            Map<String, Object> priceData2 = offerSection.get("price").get(1);
            item2 = deserializeItemStackSafely(priceData2, "price item 2");
            if (item2 != null) {
              item2 = setResourceName(item2);
            }
          } catch (Exception e) {
            BedwarsRel.getInstance().getLogger().warning("解析商店价格物品2失败 (类别: " + section.getString(cat + ".name") + "): " + e.getMessage());
          }
        }
        ItemStack reward = null;

        try {
          Map<String, Object> rewardData = offerSection.get("reward").get(0);
          reward = deserializeItemStackSafely(rewardData, "reward item");
        } catch (Exception e) {
          BedwarsRel.getInstance().getLogger().warning("解析商店奖励物品失败 (类别: " + section.getString(cat + ".name") + "): " + e.getMessage());
        }

        if (item1 == null || reward == null) {
          BedwarsRel.getInstance().getServer().getConsoleSender().sendMessage(
              ChatWriter.pluginMessage(ChatColor.RED + "Couldn't parse item in category \""
                  + section.getString(cat + ".name") + "\": " + offerSection.toString()));
          BedwarsRel.getInstance().getLogger().info("详细信息 - item1: " + (item1 != null ? item1.getType() : "null") +
                                                   ", reward: " + (reward != null ? reward.getType() : "null"));
          continue;
        }

        VillagerTrade tradeObj = null;

        if (item2 != null) {
          tradeObj = new VillagerTrade(item1, item2, reward);
        } else {
          tradeObj = new VillagerTrade(item1, reward);
        }

        offers.add(tradeObj);
      }

      mc.put(catItem, new MerchantCategory(catName, catItem, offers, lores, order, permission));
    }

    return mc;
  }

  @SuppressWarnings("deprecation")
  public static void openCategorySelection(Player p, Game g) {
    List<MerchantCategory> cats = g.getOrderedItemShopCategories();

    int nom = (cats.size() % 9 == 0) ? 9 : (cats.size() % 9);
    int size = (cats.size() + (9 - nom)) + 9;

    Inventory inv = Bukkit.createInventory(p, size, BedwarsRel._l(p, "ingame.shop.name"));
    for (MerchantCategory cat : cats) {
      if (p != null && !p.hasPermission(cat.getPermission())) {
        continue;
      }

      // 检查材料是否为null，跳过无效的类别
      if (cat.getMaterial() == null) {
        BedwarsRel.getInstance().getServer().getConsoleSender()
            .sendMessage(ChatWriter.pluginMessage(ChatColor.RED
                + "跳过商店类别 '" + cat.getName() + "'：材料为null"));
        continue;
      }

      ItemStack is = new ItemStack(cat.getMaterial(), 1);
      ItemMeta im = is.getItemMeta();

      if (Utils.isColorable(is)) {
        is.setDurability(g.getPlayerTeam(p).getColor().getDyeColor().getWoolData());
      }

      im.setDisplayName(cat.getName());
      im.setLore(cat.getLores());
      is.setItemMeta(im);

      inv.addItem(is);
    }

    ItemStack snow = new ItemStack(MaterialCompatibility.getCompatibleMaterial("SNOW_BALL"), 1);
    ItemMeta snowMeta = snow.getItemMeta();

    snowMeta.setDisplayName(BedwarsRel._l(p, "ingame.shop.newshop"));
    snowMeta.setLore(new ArrayList<String>());
    snow.setItemMeta(snowMeta);

    inv.setItem(size - 5, snow);
    p.openInventory(inv);
  }

  @SuppressWarnings("deprecation")
  private static ItemStack setResourceName(ItemStack item) {

    ItemMeta im = item.getItemMeta();
    String name = im.getDisplayName();

    // check if is ressource
    ConfigurationSection resourceSection =
        BedwarsRel.getInstance().getConfig().getConfigurationSection("resource");
    for (String key : resourceSection.getKeys(false)) {

      List<Object> resourceList = (List<Object>) BedwarsRel.getInstance().getConfig()
          .getList("resource." + key + ".item");

      for (Object resource : resourceList) {
        ItemStack itemStack = ItemStack.deserialize((Map<String, Object>) resource);
        if (itemStack != null && itemStack.getType().equals(item.getType())
            && itemStack.getItemMeta() != null
            && itemStack.getItemMeta().getDisplayName() != null) {
          name =
              ChatColor.translateAlternateColorCodes('&', itemStack.getItemMeta().getDisplayName());
        }
      }
    }

    im.setDisplayName(name);
    item.setItemMeta(im);

    return item;
  }

  @SuppressWarnings("unchecked")
  public ArrayList<VillagerTrade> getFilteredOffers() {
    ArrayList<VillagerTrade> trades = (ArrayList<VillagerTrade>) this.offers.clone();
    Iterator<VillagerTrade> iterator = trades.iterator();

    while (iterator.hasNext()) {
      VillagerTrade trade = iterator.next();
      if (trade.getItem1().getType() == Material.AIR
          && trade.getRewardItem().getType() == Material.AIR) {
        iterator.remove();
      }
    }

    return trades;
  }

  public List<String> getLores() {
    return this.lores;
  }

  public Material getMaterial() {
    return this.item;
  }

  public String getName() {
    return this.name;
  }

  public ArrayList<VillagerTrade> getOffers() {
    return this.offers;
  }

  public int getOrder() {
    return this.order;
  }

  public String getPermission() {
    return this.permission;
  }

  /**
   * 安全地反序列化ItemStack，处理1.20版本兼容性问题
   *
   * @param data 物品数据
   * @param itemType 物品类型描述（用于日志）
   * @return 反序列化的ItemStack，失败时返回null
   */
  private static ItemStack deserializeItemStackSafely(Map<String, Object> data, String itemType) {
    try {
      // 首先尝试标准反序列化
      return ItemStack.deserialize(data);
    } catch (Exception e) {
      // 如果标准反序列化失败，尝试手动构建
      try {
        String materialName = (String) data.get("type");
        if (materialName == null) {
          BedwarsRel.getInstance().getLogger().warning("物品数据中缺少type字段: " + data);
          return null;
        }

        // 使用MaterialCompatibility获取兼容的材料
        Material material = MaterialCompatibility.getCompatibleMaterial(materialName);
        if (material == null) {
          BedwarsRel.getInstance().getLogger().warning("无法找到材料: " + materialName + " (物品类型: " + itemType + ")");
          return null;
        }

        int amount = data.containsKey("amount") ? (Integer) data.get("amount") : 1;
        ItemStack itemStack = new ItemStack(material, amount);

        // 处理meta数据
        if (data.containsKey("meta")) {
          try {
            @SuppressWarnings("unchecked")
            Map<String, Object> metaData = (Map<String, Object>) data.get("meta");
            ItemMeta meta = itemStack.getItemMeta();

            if (meta != null && metaData != null) {
              // 处理显示名称
              if (metaData.containsKey("display-name")) {
                meta.setDisplayName(metaData.get("display-name").toString());
              }

              // 处理lore
              if (metaData.containsKey("lore")) {
                @SuppressWarnings("unchecked")
                List<String> lore = (List<String>) metaData.get("lore");
                meta.setLore(lore);
              }

              itemStack.setItemMeta(meta);
            }
          } catch (Exception metaEx) {
            BedwarsRel.getInstance().getLogger().warning("处理物品meta数据失败 (物品类型: " + itemType + "): " + metaEx.getMessage());
          }
        }

        return itemStack;
      } catch (Exception fallbackEx) {
        BedwarsRel.getInstance().getLogger().warning("手动构建ItemStack失败 (物品类型: " + itemType + "): " + fallbackEx.getMessage());
        return null;
      }
    }
  }

}
