package io.github.bedwarsrel.game;

import com.google.common.collect.ImmutableMap;
import io.github.bedwarsrel.BedwarsRel;
import io.github.bedwarsrel.events.BedwarsGameEndEvent;
import io.github.bedwarsrel.statistics.PlayerStatistic;
import io.github.bedwarsrel.utils.ChatWriter;
import io.github.bedwarsrel.utils.Utils;
import java.util.ArrayList;
import java.util.List;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.entity.Player;

public class SingleGameCycle extends GameCycle {

  public SingleGameCycle(Game game) {
    super(game);
  }

  private void kickPlayer(Player player, boolean wasSpectator) {
    for (Player freePlayer : this.getGame().getFreePlayers()) {
      player.showPlayer(freePlayer);
    }

    if (wasSpectator && this.getGame().isFull()) {
      this.getGame().playerLeave(player, false);
      return;
    }

    // 确保玩家总是被传送到正确的位置
    if (BedwarsRel.getInstance().toMainLobby()) {
      Location mainLobby = this.getGame().getMainLobby();
      if (mainLobby != null) {
        player.teleport(mainLobby);
      } else {
        // 如果主大厅未设置，传送到游戏大厅
        player.teleport(this.getGame().getLobby());
      }

      if (BedwarsRel.getInstance().allPlayersBackToMainLobby()) {
        // 传送后再调用playerLeave进行清理
        this.getGame().playerLeave(player, false);
        return;
      }
    } else {
      player.teleport(this.getGame().getLobby());
    }

    if (BedwarsRel.getInstance().isHologramsEnabled()
        && BedwarsRel.getInstance().getHolographicInteractor() != null
        && this.getGame().getLobby() == player.getWorld()) {
      BedwarsRel.getInstance().getHolographicInteractor().updateHolograms(player);
    }

    if (BedwarsRel.getInstance().statisticsEnabled()) {
      PlayerStatistic statistic =
          BedwarsRel.getInstance().getPlayerStatisticManager().getStatistic(player);
      BedwarsRel.getInstance().getPlayerStatisticManager().storeStatistic(statistic);

      if (BedwarsRel.getInstance().getBooleanConfig("statistics.show-on-game-end", true)) {
        BedwarsRel.getInstance().getServer().dispatchCommand(player, "bw stats");
      }
    }

    this.getGame().setPlayerDamager(player, null);

    PlayerStorage storage = this.getGame().getPlayerStorage(player);
    storage.clean();
    storage.loadLobbyInventory(this.getGame());
  }

  @Override
  public void onGameEnds() {
    // Reset scoreboard first
    this.getGame().resetScoreboard();

    // First team players, they get a reserved slot in lobby
    for (Player p : this.getGame().getTeamPlayers()) {
      this.kickPlayer(p, false);
    }

    // and now the spectators
    List<Player> freePlayers = new ArrayList<Player>(this.getGame().getFreePlayers());
    for (Player p : freePlayers) {
      this.kickPlayer(p, true);
    }

    // reset countdown prevention breaks
    this.setEndGameRunning(false);

    // Reset team chests
    for (Team team : this.getGame().getTeams().values()) {
      team.setInventory(null);
      team.getChests().clear();
    }

    // clear protections
    this.getGame().clearProtections();

    // reset region
    this.getGame().resetRegion();

    // Restart lobby directly?
    if (this.getGame().isStartable() && this.getGame().getLobbyCountdown() == null) {
      GameLobbyCountdown lobbyCountdown = new GameLobbyCountdown(this.getGame());
      lobbyCountdown.runTaskTimer(BedwarsRel.getInstance(), 20L, 20L);
      this.getGame().setLobbyCountdown(lobbyCountdown);
    }

    // set state and with that, the sign
    this.getGame().setState(GameState.WAITING);
    this.getGame().updateScoreboard();
  }

  @Override
  public void onGameLoaded() {
    // Reset on game end
  }

  @Override
  public void onGameOver(GameOverTask task) {
    if (task.getCounter() == task.getStartCount() && task.getWinner() != null) {
      for (Player aPlayer : this.getGame().getPlayers()) {
        if (aPlayer.isOnline()) {
          aPlayer.sendMessage(
              ChatWriter.pluginMessage(ChatColor.GOLD + BedwarsRel._l(aPlayer, "ingame.teamwon",
                  ImmutableMap.of("team", task.getWinner().getDisplayName() + ChatColor.GOLD))));
        }
      }
      this.getGame().stopWorkers();
    } else if (task.getCounter() == task.getStartCount() && task.getWinner() == null) {
      for (Player aPlayer : this.getGame().getPlayers()) {
        if (aPlayer.isOnline()) {
          aPlayer.sendMessage(
              ChatWriter.pluginMessage(ChatColor.GOLD + BedwarsRel._l(aPlayer, "ingame.draw")));
        }
      }
    }

    if (task.getCounter() == 0) {
      // 游戏结束倒计时完成，强制踢出所有玩家
      BedwarsGameEndEvent endEvent = new BedwarsGameEndEvent(this.getGame());
      BedwarsRel.getInstance().getServer().getPluginManager().callEvent(endEvent);

      // 强制踢出所有剩余玩家（包括获胜者）
      this.getGame().kickAllPlayers();

      this.onGameEnds();
      task.cancel();
    } else if (this.getGame().getPlayers().size() == 0) {
      // 如果所有玩家都已经离开，直接结束游戏
      BedwarsGameEndEvent endEvent = new BedwarsGameEndEvent(this.getGame());
      BedwarsRel.getInstance().getServer().getPluginManager().callEvent(endEvent);

      this.onGameEnds();
      task.cancel();
    } else {
      // 显示倒计时消息给所有剩余玩家（包括观察者）
      // 获取所有玩家：队伍玩家 + 观察者
      ArrayList<Player> allPlayers = new ArrayList<Player>();
      allPlayers.addAll(this.getGame().getTeamPlayers());
      allPlayers.addAll(this.getGame().getFreePlayers());

      for (Player aPlayer : allPlayers) {
        if (aPlayer.isOnline()) {
          aPlayer.sendMessage(
              ChatWriter.pluginMessage(
                  ChatColor.AQUA + BedwarsRel
                      ._l(aPlayer, "ingame.backtolobby", ImmutableMap.of("sec",
                          ChatColor.YELLOW.toString() + task.getCounter() + ChatColor.AQUA))));
        }
      }
    }

    task.decCounter();
  }

  @Override
  public void onGameStart() {
    // Reset on game end
  }

  @Override
  public boolean onPlayerJoins(Player player) {
    if (this.getGame().isFull() && !player.hasPermission("bw.vip.joinfull")) {
      if (this.getGame().getState() != GameState.RUNNING
          || !BedwarsRel.getInstance().spectationEnabled()) {
        player.sendMessage(
            ChatWriter.pluginMessage(ChatColor.RED + BedwarsRel._l(player, "lobby.gamefull")));
        return false;
      }
    } else if (this.getGame().isFull() && player.hasPermission("bw.vip.joinfull")) {
      if (this.getGame().getState() == GameState.WAITING) {
        List<Player> players = this.getGame().getNonVipPlayers();

        if (players.size() == 0) {
          player.sendMessage(
              ChatWriter
                  .pluginMessage(ChatColor.RED + BedwarsRel._l(player, "lobby.gamefullpremium")));
          return false;
        }

        Player kickPlayer = null;
        if (players.size() == 1) {
          kickPlayer = players.get(0);
        } else {
          kickPlayer = players.get(Utils.randInt(0, players.size() - 1));
        }

        kickPlayer
            .sendMessage(
                ChatWriter.pluginMessage(ChatColor.RED + BedwarsRel
                    ._l(kickPlayer, "lobby.kickedbyvip")));
        this.getGame().playerLeave(kickPlayer, false);
      } else {
        if (this.getGame().getState() == GameState.RUNNING
            && !BedwarsRel.getInstance().spectationEnabled()) {
          player.sendMessage(
              ChatWriter
                  .pluginMessage(ChatColor.RED + BedwarsRel._l(player, "errors.cantjoingame")));
          return false;
        }
      }
    }

    return true;
  }

  @Override
  public void onPlayerLeave(Player player) {
    // teleport to join location
    PlayerStorage storage = this.getGame().getPlayerStorage(player);

    if (BedwarsRel.getInstance().toMainLobby()) {
      // 传送到主大厅
      Location mainLobby = this.getGame().getMainLobby();
      if (mainLobby != null) {
        if (BedwarsRel.getInstance().isHologramsEnabled()
            && BedwarsRel.getInstance().getHolographicInteractor() != null
            && mainLobby.getWorld() == player.getWorld()) {
          BedwarsRel.getInstance().getHolographicInteractor().updateHolograms(player);
        }
        player.teleport(mainLobby);
      } else {
        // 如果主大厅未设置，传送到玩家原来的位置
        if (storage != null && storage.getLeft() != null) {
          player.teleport(storage.getLeft());
        }
      }
    } else {
      // 传送到玩家原来的位置
      if (storage != null && storage.getLeft() != null) {
        if (BedwarsRel.getInstance().isHologramsEnabled()
            && BedwarsRel.getInstance().getHolographicInteractor() != null
            && storage.getLeft().getWorld() == player.getWorld()) {
          BedwarsRel.getInstance().getHolographicInteractor().updateHolograms(player);
        }
        player.teleport(storage.getLeft());
      } else {
        // 如果没有存储位置，传送到游戏大厅
        Location gameLobby = this.getGame().getLobby();
        if (gameLobby != null) {
          player.teleport(gameLobby);
        }
      }
    }

    if (this.getGame().getState() == GameState.RUNNING && !this.getGame().isStopping()
        && !this.getGame().isSpectator(player)) {
      this.checkGameOver();
    }
  }

}
