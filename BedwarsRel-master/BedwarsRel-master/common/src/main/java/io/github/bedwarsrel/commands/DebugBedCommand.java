package io.github.bedwarsrel.commands;

import com.google.common.collect.ImmutableMap;
import io.github.bedwarsrel.BedwarsRel;
import io.github.bedwarsrel.game.Game;
import io.github.bedwarsrel.game.Team;
import io.github.bedwarsrel.utils.ChatWriter;
import io.github.bedwarsrel.utils.MaterialCompatibility;
import io.github.bedwarsrel.utils.Utils;
import java.util.ArrayList;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class DebugBedCommand extends BaseCommand implements ICommand {

  public DebugBedCommand(BedwarsRel plugin) {
    super(plugin);
  }

  @Override
  public boolean execute(CommandSender sender, ArrayList<String> args) {
    if (!sender.hasPermission("bw." + this.getPermission())) {
      return false;
    }

    Player player = (Player) sender;
    Game game = this.getPlugin().getGameManager().getGame(args.get(0));

    if (game == null) {
      sender.sendMessage(ChatWriter.pluginMessage(ChatColor.RED
          + BedwarsRel
          ._l(sender, "errors.gamenotfound", ImmutableMap.of("game", args.get(0).toString()))));
      return false;
    }

    player.sendMessage(ChatWriter.pluginMessage(ChatColor.YELLOW + "=== 床检测调试信息 ==="));
    
    Material targetMaterial = game.getTargetMaterial();
    player.sendMessage(ChatWriter.pluginMessage(ChatColor.AQUA + "目标材料: " + targetMaterial));
    player.sendMessage(ChatWriter.pluginMessage(ChatColor.AQUA + "是否为床材料: " + MaterialCompatibility.isBedMaterial(targetMaterial)));
    
    for (Team team : game.getTeams().values()) {
      player.sendMessage(ChatWriter.pluginMessage(ChatColor.GREEN + "--- 队伍: " + team.getName() + " ---"));
      
      // 检查重生点
      if (team.getSpawnLocation() == null) {
        player.sendMessage(ChatWriter.pluginMessage(ChatColor.RED + "  重生点: 未设置"));
      } else {
        player.sendMessage(ChatWriter.pluginMessage(ChatColor.GREEN + "  重生点: 已设置 " + team.getSpawnLocation()));
      }
      
      // 检查床头部
      if (team.getHeadTarget() == null) {
        player.sendMessage(ChatWriter.pluginMessage(ChatColor.RED + "  床头部: 未设置"));
      } else {
        player.sendMessage(ChatWriter.pluginMessage(ChatColor.YELLOW + "  床头部: " + team.getHeadTarget().getLocation()));
        player.sendMessage(ChatWriter.pluginMessage(ChatColor.YELLOW + "  床头部材料: " + team.getHeadTarget().getType()));
        player.sendMessage(ChatWriter.pluginMessage(ChatColor.YELLOW + "  是否为床方块: " + Utils.isBedBlock(team.getHeadTarget())));
      }
      
      // 检查床脚部
      if (team.getFeetTarget() == null) {
        player.sendMessage(ChatWriter.pluginMessage(ChatColor.RED + "  床脚部: 未设置"));
      } else {
        player.sendMessage(ChatWriter.pluginMessage(ChatColor.YELLOW + "  床脚部: " + team.getFeetTarget().getLocation()));
        player.sendMessage(ChatWriter.pluginMessage(ChatColor.YELLOW + "  床脚部材料: " + team.getFeetTarget().getType()));
        player.sendMessage(ChatWriter.pluginMessage(ChatColor.YELLOW + "  是否为床方块: " + Utils.isBedBlock(team.getFeetTarget())));
      }
      
      // 检查床检测结果
      if (MaterialCompatibility.isBedMaterial(targetMaterial)) {
        boolean bedValid = (team.getHeadTarget() != null && team.getFeetTarget() != null)
            && (Utils.isBedBlock(team.getHeadTarget()) && Utils.isBedBlock(team.getFeetTarget()));
        player.sendMessage(ChatWriter.pluginMessage(ChatColor.AQUA + "  床检测结果: " + (bedValid ? ChatColor.GREEN + "通过" : ChatColor.RED + "失败")));
      }
    }
    
    // 执行游戏检查
    player.sendMessage(ChatWriter.pluginMessage(ChatColor.YELLOW + "=== 游戏检查结果 ==="));
    try {
      // 使用反射调用私有方法
      java.lang.reflect.Method checkTeamsMethod = game.getClass().getDeclaredMethod("checkTeams");
      checkTeamsMethod.setAccessible(true);
      Object result = checkTeamsMethod.invoke(game);
      player.sendMessage(ChatWriter.pluginMessage(ChatColor.AQUA + "checkTeams() 结果: " + result));
    } catch (Exception e) {
      player.sendMessage(ChatWriter.pluginMessage(ChatColor.RED + "无法执行 checkTeams(): " + e.getMessage()));
    }
    
    return true;
  }

  @Override
  public String[] getArguments() {
    return new String[]{"game"};
  }

  @Override
  public String getCommand() {
    return "debugbed";
  }

  @Override
  public String getDescription() {
    return "调试床检测问题";
  }

  @Override
  public String getName() {
    return "调试床检测";
  }

  @Override
  public String getPermission() {
    return "setup";
  }

}
