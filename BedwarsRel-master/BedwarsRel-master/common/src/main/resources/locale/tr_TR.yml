---
default:
  pages: "$max$ sayfadan $current$ ci"
  currently: Şuanda
errors:
  argumentslength: "Bağımsız değişkenleri doğru miktarı aynı değil!"
  holodependencynotfound: "Hologram Istatikleri için $dependency$ bulunamadı"
  packagenotfound: "$package$ paketi çekilemedi!"
  classnotfound: "Couldn't fetch $package$ class $class$!"
  gameexists: "Bu isimde bir oyun zaten var!"
  gamenotfound: "Oyun '$game$' bulunamadı!"
  nofreegames: "Hiçbir ücretsiz oyunlar mevcuttur."
  gamenotfoundsimple: "Oyun bulunamadı!"
  playeramount: "Maksimum oyuncu sayısı 1'den küçük 24'ten büyük olamaz!"
  teamcolornotallowed: "Takım rengi izin verilen renk değildir"
  teamnamelength: "Takım ismi 2 ile 20 karakter arasında olmak zorundadır!"
  teamnotfound: "Takım bulunamadı!"
  notingame: "<PERSON>uan'da bir oyunda değilsin!"
  bedtargeting: "You have to target or stand on a block of the configured 'game-block' type!"
  regionargument: "Your location argument has to be 'loc1' or 'loc2'!"
  spawnerargument: "The resource parameter has to be a valid configured resource!"
  blockdownnotfound: "Üzerinde durduğuna blok bulunamadı!"
  gamenotrunning: "Oyun çalışmıyor!"
  bungeenoserver: "Bungeecord Sunucuları doğru ayarlanmadı! Sunucu yöneticisi ile konuşun!"
  cantstartagain: "Game is running! You can't start a running game again!"
  startoutofwaiting: "Game have to be started out of the waiting mode!"
  cantjoingame: "Çalışan veya durdurulmuş bir oyuna katılamazsın!"
  lobbyongameworld: "Lobi oyun dünyasında olamaz!"
  gamenotloaded: "Oyun başlatılamadı!"
  gameloaderror: "Loading game '$game$' throws an error!"
  regionnotfound: "Bölge dosyası yok!"
  savesign: "Couldn't create a new sign config file!"
  nogames: "Oyun bulunamadı!"
  notenoughress: "You didn't have enough ressource to buy this item!"
  teamnameinuse: "Team name is already in use!"
  minplayersmustnumber: "Min players must be a number!"
  toolongregionname: "Maximum length of region name are 15 characters!"
  notwhilegamerunning: "Cannot do that while game is running!"
  notwhileingame: "Cannot do that while you're in a running game!"
  timeincorrect: "Time has to be a number (0 ... 23000), 'day' or 'night'!"
  minplayersnumeric: "The min-players parameter must be numeric!"
  notinair: "You're not in the air!"
  teamfull: "The team is full, please reopen the team selection menu!"
  novalidmaterial: "Given block type (material) isn't correct!"
  wrongvalueonoff: "Wrong Parameter! Use true,on,1 to turn on - Use false,off,0 to turn off!"
  playernotfound: "Given player was not found or isn't online!"
  notingameforkick: "You must be in a game to kick a player!"
  playernotingame: "Given player is not in this game!"
  mustbeinlobbyworld: "You must be in the lobby world of the game"
  addteamjoincancel: "The adding of team join was cancelled!"
  entitynotcompatible: "This entity is not compatible with team join!"
success:
  gameadded: "New game '$game$' successfully added!"
  teamadded: "Team '$team$' successfully added!"
  joined: "Oyuna Başarıyla Katıldın!"
  left: "Başarılı bir şekilde oyunu bıraktın!"
  saved: "Oyunu başarıyla kaydedildi!"
  bedset: "$team$ takımının yeniden doğma bloğu ayarlandı!"
  regionset: "Game region location $location$ for Game $game$ was set successfully!"
  spawnset: "Spawn location for Team $team$ was set successfully!"
  spawnerset: "Ressource spawn location for $name$ was set successfully!"
  stopped: "Oyunu başarıyla durduruldu!"
  lobbyset: "Lobi başarıyla kuruldu!"
  gameloaded: "Game '$game$' successfully loaded!"
  reloadconfig: "Yeniden başlatma başarılı."
  teamremoved: "Takım Başarıyla Silindi.\n"
  gameremoved: "Oyun Başarıyla Silindi."
  spawnercleared: "Tüm Spawnerlar Silindi.\n"
  gamerun: "You started the game, players can now join!"
  timeset: "Oyun Saati Başarıyla Ayarlandı.\n"
  regionnameset: "Bölge ismi oluşturuldu!"
  minplayersset: "En Az Oyuncu Belirlendi.\n"
  mainlobbyset: "Mainlobi Başarıyla Ayarlandı.\n"
  gametimeset: "Oyun Süresi Başarıyla Ayarlandı.\n"
  materialset: "The respawn block type (material) was set succesfully!"
  builderset: "The builder for the map was set successfully and will display in title!"
  autobalanceseton: "Autobalance was successfully turned on!"
  autobalancesetoff: "Autobalance was successfully turned &coff&a!"
  selectteamjoinentity: "Now do a right click on the entity you want to use as team join!"
  teamjoinadded: "Entity was successfully marked as team selection for team $team$"
  holoremoved: "Hologram-Statistic successfully removed!"
gamecheck:
  LOC_NOT_SET_ERROR: "Locations for the region were not set properly!"
  TEAM_SIZE_LOW_ERROR: "You have to set more teams!"
  NO_RES_SPAWNER_ERROR: "You didn't set any resource spawner!"
  NO_LOBBY_SET: "Sen Lobiyi Ayarlamadın."
  TEAMS_WITHOUT_SPAWNS: "There are team(s) without a spawn location!"
  NO_ITEMSHOP_CATEGORIES: "No itemshop categories found!"
  NO_MAIN_LOBBY_SET: "You didn't set a main lobby even though you did set 'tomainlobby' to true"
  TEAM_NO_WRONG_BED: "One or more teams have no bed set!"
  TEAM_NO_WRONG_TARGET: "One or more teams have no respawn block set!"
ingame:
  team: "Takım"
  teams: "Takımlar"
  all: "Tümü"
  record: "&e$record$&a is the record on this map!"
  record-with-holders: '&aThe record on this map is &e$record$&a and is held by: $holders$'
  newrecord: '&aTeam $team$&a set a new record: &6$record$'
  record-nobeddestroy: "&cRecord won't be saved, because no bed was destroyed!"
  teamwon: "Congratulations! Team $team$ won!"
  draw: "The game ends with a draw!"
  serverrestart: "Server restart in $sec$ second(s)!"
  gamestarting: "Oyun Başlatılıyor..."
  gamestarted: "Game '$game$' has just started!"
  backtolobby: "Back to lobby in $sec$ second(s)!"
  spectator: "İzleyiciler"
  spectate: "&aİzleyici"
  teamchest: "Takım Sandığı"
  noturteamchest: "This chest isn't a chest of your team!"
  protectionleft: "Invulnerable for &c$length$&f second(s)!"
  protectionend: "You're now &cvulnerable&f again!"
  team-dead: "Team $team$ was destroyed!"
  no-friendlybreak: "&cCan't break block under team-member!"
  teamchestdestroy: "&cOne of your teamchest(s) has been destroyed!"
  title:
    map-builder: "$builder$ Tarafından İnşa Edildi"
    win-title: "&6Tebrikler"
    win-subtitle: "$team$&6 won in &e$time$"
  shop:
    name: "Market"
    newshop: "Yeni dükkan kullan"
    oldshop: "Eski marketi kullan"
    fullstackpershift: "Multiply stacks per shift click"
    onestackpershift: "One stack per shift click"
  player:
    left: "Player $player$ has left the game!"
    died: "$player$ died!"
    killed: "$killer$ killed $player$!"
    kicked: "$player$ was kicked!"
    waskicked: "You were kicked from the game!"
  blocks:
    ownbeddestroy: "You can't destroy your own bed!"
    beddestroyed: "$player$ destroyed bed of team $team$!"
  specials:
    rescue-platform:
      left: "There are &c$time$&f second(s) left until you can use the next rescue platform!"
    arrow-blocker:
      start: "You are protected from being hit by arrows for &c$time$&f second(s)."
      end: "&cYour arrow protection is over"
      left: "There are &c$time$&f second(s) left until you can use the next arrowblocker!"
    trap:
      trapped: "&eSomeone went into a &ctrap&e of your team!"
    protection-wall:
      left: "There are &c$time$&f second(s) left until you can use the next protection wall!"
      not-usable-here: "You cannot use the protection wall here!"
    warp-powder:
      cancelled: "&cYour teleport was cancelled!"
      start: "You will be teleported in &c$time$&f second(s). Don't move!"
      cancel: "&4Işınlanma İptal Edildi"
      multiuse: "&cYou started a teleportation already!"
    tntsheep:
      no-target-found: "Hedef Oyuncu Bulunamadı!"
    tracker:
      no-target-found: "Hedef Oyuncu Bulunamadı!"
      target-found: " $player$ Senden $block$ Blok Ötede."
lobby:
  countdown: "Oyunun Başlamasına $sec$ Saniye Kaldı."
  countdowncancel: "Az Oyuncu Olduğundan Geri Sayım Durduruldu! "
  cancelcountdown:
    not_enough_players: "Az Oyuncu Olduğundan Geri Sayım Durduruldu!"
    not_enough_teams: "Yeteri Kadar Takım Olmadığından Geri Sayım Durduruldu!\n"
  cancelstart:
    not_enough_players: "Oyunun Başlaması İçin Daha Fazla Oyuncuya İhtiyaç Var."
    not_enough_teams: "Yeteri Kadar Takım Olmadığından Oyun Başlatılamadı!"
  chooseteam: "Takım Seç"
  startgame: "Oyunu Başlat"
  reduce_countdown: "Geri Sayımı Azalt"
  gamefull: "Oyun Dolu!"
  gamefullpremium: "Oda Dolu Olduğundan Giriş Yapamıyorsun Ancak Vip Alarak Giriş Yapabilirsin!"
  teamjoined: "Başarıyla $team$ Takıma Girdin"
  leavegame: "Odadan Ayrıl"
  playerjoin: "$player$ Oyuna Katıldı!"
  kickedbyvip: "Yetkili Tarafından Oyundan Atıldınız!"
  moreplayersneeded: "$count$ daha fazla oyuncu gerek!"
  moreplayersneeded-one: "$count$ daha fazla oyuncu gerek!"
  moreteamsneeded: "Oyunun Başlaması İçin Odada En Az 2 Oyuncu Olması Gerekir!"
sign:
  firstline: "&6[Bedwars]"
  players: "Oyuncu"
  gamestate:
    stopped: "&4Durduruldu!"
    waiting: "&aBekleniyor ..."
    running: "&9Devam Ediyor!"
    full: "Dolu!"
stats:
  header: "Bedwars İstatistikleri"
  kd: "Ölüm/Öldürme Oranı"
  statsnotfound: "Statistics of $player$ not found!"
  name: "İsim"
  kills: "Öldürme"
  deaths: "Ölme"
  wins: "Kazanma"
  loses: "Kaybetme"
  score: "Skorlar"
  destroyedBeds: "Yatak Kırma"
  games: "Oyun"
commands:
  addgame:
    name: "Oyun Ekle"
    desc: "Yeni Oyunlar Eklendi"
  addteam:
    name: "Takım Ekle"
    desc: "Adds a team to a specific game"
  join:
    name: "Oyuna Katıl"
    desc: "Joins a specific game"
  leave:
    name: "Leave Game"
    desc: "Leave the current game"
  save:
    name: "Save Game"
    desc: "Saves a game (and map) to config file(s)"
  settarget:
    name: "Set target"
    desc: "Sets the location of a team's target block"
  setbed:
    name: "Set bed (synonym for settarget)"
    desc: "Sets the location of a team's bed block (Synonym for settarget)"
  setlobby:
    name: "Set lobby"
    desc: "Oyun Lobisinin bölgesini ayarlar"
  setregion:
    name: "bir bölge noktası oluşturur"
    desc: "oyunun bölge noktasını oluşturur"
  setspawn:
    name: "Takımın doğacağı yeri ayarlar"
    desc: "Sets the spawn of the given team"
  setspawner:
    name: "Spawner Koy"
    desc: "Sets a spawner location of a specific ressource"
  start:
    name: "Oyunu Başlat"
    desc: "Oyun başlatır"
  stop:
    name: "Oyunu Durdurur."
    desc: "Bir oyunu durdurur"
  help:
    name: "Yardım sayfasını görüntüler"
    desc: "Display information about the plugin and its commands"
  reload:
    name: "Yeniden yükler"
    desc: "Reloads the configurations and translations"
  setmainlobby:
    name: "MainLobiyi Ayarla"
    desc: "Sets the main lobby of a game (is needed when mainlobby-enabled is set to true)"
  list:
    name: "Oyunları Listele"
    desc: "Lists all available games"
  regionname:
    name: "Set region name"
    desc: "Sets an individual region name (instead of world name)"
  removeteam:
    name: "Takım Sil"
    desc: "Removes a team from the game (only in stopped mode)"
  removegame:
    name: "Oyunu Sil"
    desc: "Removes a game and every configuration"
  clearspawner:
    name: "Spawnerları Sil"
    desc: "Removes all spawners from the game. Saving needed."
  gametime:
    name: "Set game time"
    desc: "Sets the game time which should be used in the game-world"
  stats:
    name: "İstatistikleri"
    desc: "Shows your statistics"
  setbuilder:
    name: "Sets builder of map"
    desc: "Sets the builder of the map which will be display in the title when game starts."
  setgameblock:
    name: "Oyun Bloğunu Ayarla"
    desc: "Sets the game block type for this game which should be used instead of the 'game-block' configuration. Write 'DEFAULT' as type to use the type of config again"
  setautobalance:
    name: "Set autobalance"
    desc: "If 'global-autobalance' is set to 'false', with this command you can set team-autobalance per game to on or off!"
  setminplayers:
    name: "En Az Oyuncuyu Ayarla"
    desc: "Sets the amount of players needed to start the game"
  kick:
    name: "Oyuncuyu Oyundan At"
    desc: "Kicks a player from the current game!"
  addteamjoin:
    name: "Add team selection"
    desc: "Mark a creature which can be used to join a specific team!"
  addholo:
    name: "Hologram Ekle"
    desc: "A hologram statistic will be added at the current position!"
  removeholo:
    name: "Belirli Hologramı Yoket"
    desc: "When the command was executed, the player can right-click the hologram which should be removed."
    explain: "Perform a left click within 10 seconds on the hologram you want to remove."
  debugpaste:
    name: "Paste debug data"
    desc: "This will send some debug data to hastebin and returns a link you can share with the developers"
  itemspaste:
    name: "Paste your inventory to a file"
    desc: "This will return a link where you can see your current inventory being serialized as an example for your shop.yml"
