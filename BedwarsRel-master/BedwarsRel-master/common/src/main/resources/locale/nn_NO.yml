---
default:
  pages: "Side $current$ av $max$"
  currently: For øyeblikket
errors:
  argumentslength: "Antall argumenter samsvarer ikke riktig beløp!"
  holodependencynotfound: "Kunne ikke finne $dependency$ for Hologram-statistikken"
  packagenotfound: "Kunne ikke finne $package$ package!"
  classnotfound: "Couldn't fetch $package$ class $class$!"
  gameexists: "Et spill med dette navnet finnes allerede."
  gamenotfound: "Runden '$game$' kunne ikke bli funnet!"
  nofreegames: "Det er ingen ledige spill tilgjengelige."
  gamenotfoundsimple: "Spill ikke funnet!"
  playeramount: "Maks antall spillere kan ikke være lavere enn 1 eller høyere enn 24!"
  teamcolornotallowed: "Denne lag fargen er ikke tiltatt"
  teamnamelength: "Lag navn må ha mellom 2 og 20 tegn!"
  teamnotfound: "Team ikke funnet!"
  notingame: "Du er ikke i et spill!"
  bedtargeting: "You have to target or stand on a block of the configured 'game-block' type!"
  regionargument: "Your location argument has to be 'loc1' or 'loc2'!"
  spawnerargument: "The resource parameter has to be a valid configured resource!"
  blockdownnotfound: "The block you're standing on was not found!"
  gamenotrunning: "Spillet kjører ikke!"
  bungeenoserver: "Bungeecord Servere har ikke blitt satt opp riktig! Snakk med en adminstrator!"
  cantstartagain: "Spillet kjører! Du kan ikke starte et kjørende spill igjen!"
  startoutofwaiting: "Game have to be started out of the waiting mode!"
  cantjoingame: "Du kan ikke bli med på ett spill som kjører eller har blitt stoppet!"
  lobbyongameworld: "Lobbyen kan ikke være i spill verdenen!"
  gamenotloaded: "Kunne ikke starte spillet!"
  gameloaderror: "Loading game '$game$' throws an error!"
  regionnotfound: "Region file does not exists!"
  savesign: "Couldn't create a new sign config file!"
  nogames: "Ingen spill funnet!"
  notenoughress: "Du har ikke nok resurser til å kjøpe denne gjenstanden!"
  teamnameinuse: "Det lag navnet er allerede i bruk!"
  minplayersmustnumber: "Minimum antall spillere må være ett tall!"
  toolongregionname: "Maximum length of region name are 15 characters!"
  notwhilegamerunning: "Cannot do that while game is running!"
  notwhileingame: "Cannot do that while you're in a running game!"
  timeincorrect: "Time has to be a number (0 ... 23000), 'day' or 'night'!"
  minplayersnumeric: "The min-players parameter must be numeric!"
  notinair: "Du er ikke i luften!"
  teamfull: "Laget er fullt, åpne lag menyen på nytt!"
  novalidmaterial: "Given block type (material) isn't correct!"
  wrongvalueonoff: "Wrong Parameter! Use true,on,1 to turn on - Use false,off,0 to turn off!"
  playernotfound: "Given player was not found or isn't online!"
  notingameforkick: "Du må være i ett spill for å kaste ut en spiller!"
  playernotingame: "Denne spilleren er ikke i dette spillet!"
  mustbeinlobbyworld: "You must be in the lobby world of the game"
  addteamjoincancel: "The adding of team join was cancelled!"
  entitynotcompatible: "This entity is not compatible with team join!"
success:
  gameadded: "Nytt spill '$game$' lagt til!"
  teamadded: "Lag '$team$' lagt til!"
  joined: "Du har blitt med i spillet!"
  left: "Du har forlatt spillet!"
  saved: "Spillet ble lagret!"
  bedset: "Du satt respawn blokken til laget '$team$'!"
  regionset: "Game region location $location$ for Game $game$ was set successfully!"
  spawnset: "Spawn location for Team $team$ was set successfully!"
  spawnerset: "Ressource spawn location for $name$ was set successfully!"
  stopped: "Spillet ble stoppet!"
  lobbyset: "Lobby was set successfully!"
  gameloaded: "Game '$game$' successfully loaded!"
  reloadconfig: "Oppdatering vellykket!"
  teamremoved: "Laget ble fjernet!"
  gameremoved: "Spillet ble fjernet!"
  spawnercleared: "All ressource spawners has been removed!"
  gamerun: "You started the game, players can now join!"
  timeset: "Game time was successfully set!"
  regionnameset: "Region name was set successfully!"
  minplayersset: "Minimum antall spillere angitt!"
  mainlobbyset: "Mainlobby was set successfully!"
  gametimeset: "Game time was set successfully!"
  materialset: "The respawn block type (material) was set succesfully!"
  builderset: "The builder for the map was set successfully and will display in title!"
  autobalanceseton: "Autobalance was successfully turned on!"
  autobalancesetoff: "Autobalance was successfully turned &coff&a!"
  selectteamjoinentity: "Now do a right click on the entity you want to use as team join!"
  teamjoinadded: "Entity was successfully marked as team selection for team $team$"
  holoremoved: "Hologram-statistikken fjernet!"
gamecheck:
  LOC_NOT_SET_ERROR: "Locations for the region were not set properly!"
  TEAM_SIZE_LOW_ERROR: "You have to set more teams!"
  NO_RES_SPAWNER_ERROR: "Du har ikke angitt noen ressurs spawnere!"
  NO_LOBBY_SET: "Du har ikke angitt en lobby!"
  TEAMS_WITHOUT_SPAWNS: "There are team(s) without a spawn location!"
  NO_ITEMSHOP_CATEGORIES: "No itemshop categories found!"
  NO_MAIN_LOBBY_SET: "You didn't set a main lobby even though you did set 'tomainlobby' to true"
  TEAM_NO_WRONG_BED: "One or more teams have no bed set!"
  TEAM_NO_WRONG_TARGET: "One or more teams have no respawn block set!"
ingame:
  team: "Team"
  teams: "Lag"
  all: "Alle"
  record: "&e$record$&a er rekorden på dette mappet!"
  record-with-holders: '&aRekorden på dette mappet er &e$record$&a og er holdt av: $holders$'
  newrecord: '&aTeam $team$&a har satt en ny rekord: &6$record$'
  record-nobeddestroy: "&cRekorden blir ikke lagret, Siden ingen seng var ødelagt!"
  teamwon: "Gratulerer! Team $team$ vant!"
  draw: "Spillet ender uavgjort!"
  serverrestart: "Serveren restarter om $sec$ sekund(er)!"
  gamestarting: "Spillet starter..."
  gamestarted: "Mappet '$game$' Har nettop startet!"
  backtolobby: "Tilbake til lobby om $sec$ sekund(er)!"
  spectator: "Tilskuer"
  spectate: "&aSe på"
  teamchest: "Lag kiste"
  noturteamchest: "Dette er ikke en kiste av ditt team!"
  protectionleft: "Usårbar i &c$length$&f sekund(er)!"
  protectionend: "Du er nå &cvulnerable&f igjen!"
  team-dead: "Team $team$ ble tilintetgjort!\n \n\n"
  no-friendlybreak: "&cDu kan ikke ødlegge en blokk under lag-kammeraten din!"
  teamchestdestroy: "&cEn av dine teamchest(s) har blitt ødelagt!"
  title:
    map-builder: "Bygget av $builder$"
    win-title: "&rGratulerer!"
    win-subtitle: "$team$&6 Vant på &e$time$"
  shop:
    name: "Askelakk's Kiosk"
    newshop: "Bruk den nye shoppen"
    oldshop: "Bruk den gamle shoppen"
    fullstackpershift: "Multipliser stacks hver shift klikk"
    onestackpershift: "En stack per shift klikk"
  player:
    left: "Spilleren $player$ forlot spillet!"
    died: "$player$ døde..."
    killed: "$killer$ drepte $player$!"
    kicked: "$player$ ble kicket!"
    waskicked: "Du ble kicket fra gamet!"
  blocks:
    ownbeddestroy: "Du kan ikke ødelegge din egen seng!"
    beddestroyed: "$player$ Tilintetgjorde senga til $team$!"
  specials:
    rescue-platform:
      left: "Det er &c$time$&f Sekund(er) igjen til du kan bruke rescue platform!"
    arrow-blocker:
      start: "Du er beskyttet mot piler i &c$time$&f sekund(er)."
      end: "&cDin beskyttelse mot piler er over"
      left: "Det er &c$time$&f sekund(er) igjen før du kan bruke den neste pilbeskyttelsen!"
    trap:
      trapped: "&eNoen gikk inn i &cfella&e til ditt team!"
    protection-wall:
      left: "Det er &c$time$&f sekund(er) igjen før du kan bruke den neste beskyttelses veggen!"
      not-usable-here: "Du kan ikke bruke beskyttelses veggen her!"
    warp-powder:
      cancelled: "&cTeleportasjonen din ble avlyst!"
      start: "Du vil bli teleportert om &c$time$&f sekund(er). Ikke rør deg!"
      cancel: "&4Avbryt teleportasjon"
      multiuse: "&cDu startet en teleportasjon allerede!"
    tntsheep:
      no-target-found: "Ingen fiende funnet!"
    tracker:
      no-target-found: "Ingen fiende funnet!"
      target-found: "$player$ er $blocks$ blokk(er) unna deg."
lobby:
  countdown: "Spillet starter om $sec$ sekund(er)!"
  countdowncancel: "Flere spillere trengs. Nedtelling ble avbrutt!"
  cancelcountdown:
    not_enough_players: "Flere spillere trengs. Nedtelling avbrutt!"
    not_enough_teams: "Flere lag trengs. Nedtelling avbrutt!"
  cancelstart:
    not_enough_players: "Flere spillere trengs for å starte gamet!"
    not_enough_teams: "Flere lag trengs for å starte gamet!"
  chooseteam: "Velg et lag"
  startgame: "Start spillet"
  reduce_countdown: "Reduser nedtelling"
  gamefull: "Spillet er fullt!"
  gamefullpremium: "Spillet er fullt av premium spillere allerede!"
  teamjoined: "Du har vellykket joinet $team$ team"
  leavegame: "Forlat spillet"
  playerjoin: "Spiller $player$ ble med i spillet!"
  kickedbyvip: "Du ble kicket av en vip eller premium spiller som joinet det fulle gamet!"
  moreplayersneeded: "$count$ flere spillere trengs."
  moreplayersneeded-one: "$count$ mer spiller trengs."
  moreteamsneeded: "Minimum to spillere i to forskjellige teams trengs for å starte gamet!"
sign:
  firstline: "&6[Bedwars]"
  players: "Spillere"
  gamestate:
    stopped: "&Stoppet!"
    waiting: "&aVenter ..."
    running: "&9Kjører!"
    full: "Fullt!"
stats:
  header: "Bedwars stats"
  kd: "K/D"
  statsnotfound: "Statistikken til $player$ ble ikke funnet!"
  name: "Navn"
  kills: "Drap"
  deaths: "Dødsfall"
  wins: "Seire"
  loses: "Tap"
  score: "Poengsum"
  destroyedBeds: "Ødelagte senger"
  games: "Spill"
commands:
  addgame:
    name: "Legg til spill"
    desc: "Legger til et nytt game"
  addteam:
    name: "Legg til lag"
    desc: "Legger til et team til et spesifikt spillt"
  join:
    name: "Delta i spill"
    desc: "Joins a specific game"
  leave:
    name: "Forlat Spillet"
    desc: "Leave the current game"
  save:
    name: "Lagre Spillet"
    desc: "Saves a game (and map) to config file(s)"
  settarget:
    name: "Sett mål"
    desc: "Sets the location of a team's target block"
  setbed:
    name: "Set bed (synonym for settarget)"
    desc: "Sets the location of a team's bed block (Synonym for settarget)"
  setlobby:
    name: "Angi lobbyen"
    desc: "Sets the location of the gamelobby"
  setregion:
    name: "Sets a region point"
    desc: "Sets a region point for the game"
  setspawn:
    name: "Sets a team spawn"
    desc: "Sets the spawn of the given team"
  setspawner:
    name: "Angi spawner"
    desc: "Sets a spawner location of a specific ressource"
  start:
    name: "Start spillet"
    desc: "Starter ett spill"
  stop:
    name: "Stopp spillet"
    desc: "Stopper ett spill"
  help:
    name: "Vis hjelp"
    desc: "Display information about the plugin and its commands"
  reload:
    name: "Oppdater"
    desc: "Reloads the configurations and translations"
  setmainlobby:
    name: "Angi hovedlobbyen"
    desc: "Sets the main lobby of a game (is needed when mainlobby-enabled is set to true)"
  list:
    name: "Viser spill"
    desc: "Viser alle tilgjengelige spill"
  regionname:
    name: "Set region name"
    desc: "Sets an individual region name (instead of world name)"
  removeteam:
    name: "Fjern lag"
    desc: "Fjerner ett lag fra spillet (bare i stoppet modus)"
  removegame:
    name: "Fjerne spillet"
    desc: "Removes a game and every configuration"
  clearspawner:
    name: "Fjern spawnere"
    desc: "Removes all spawners from the game. Saving needed."
  gametime:
    name: "Angi spilletid"
    desc: "Sets the game time which should be used in the game-world"
  stats:
    name: "Statistikker"
    desc: "Viser statistikken"
  setbuilder:
    name: "Angi bygger av kart"
    desc: "Sets the builder of the map which will be display in the title when game starts."
  setgameblock:
    name: "Set game block"
    desc: "Sets the game block type for this game which should be used instead of the 'game-block' configuration. Write 'DEFAULT' as type to use the type of config again"
  setautobalance:
    name: "Set autobalance"
    desc: "If 'global-autobalance' is set to 'false', with this command you can set team-autobalance per game to on or off!"
  setminplayers:
    name: "Angi minimum spillere"
    desc: "Angir hvor mange spillere som trengs for å starte spillet"
  kick:
    name: "Kast ut spiller"
    desc: "Sparker en spiller fra spillet!"
  addteamjoin:
    name: "Add team selection"
    desc: "Mark a creature which can be used to join a specific team!"
  addholo:
    name: "Add hologram location"
    desc: "A hologram statistic will be added at the current position!"
  removeholo:
    name: "Remove hologram location"
    desc: "When the command was executed, the player can right-click the hologram which should be removed."
    explain: "Perform a left click within 10 seconds on the hologram you want to remove."
  debugpaste:
    name: "Paste debug data"
    desc: "This will send some debug data to hastebin and returns a link you can share with the developers"
  itemspaste:
    name: "Paste your inventory to a file"
    desc: "This will return a link where you can see your current inventory being serialized as an example for your shop.yml"
