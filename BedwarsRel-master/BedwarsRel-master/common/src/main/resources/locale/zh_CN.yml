---
default:
  pages: "第$current$页-共$max$页"
  currently: 目前
errors:
  argumentslength: "参数不正确!"
  holodependencynotfound: "全息显示前置插件缺失 $dependency$"
  packagenotfound: "无法获得$package$ 包!"
  classnotfound: "无法获得包 $package$ 的类$class$!"
  gameexists: "以此为名的游戏已经存在！"
  gamenotfound: "游戏 '$game$' 不存在！"
  nofreegames: "暂时没有可以加入的游戏。"
  gamenotfoundsimple: "游戏不存在!"
  playeramount: "最大玩家数不能低于1，或高于24！"
  teamcolornotallowed: "队伍颜色有误"
  teamnamelength: "队伍名必须在2-20个字符之间！"
  teamnotfound: "没有发现此队伍！"
  notingame: "你不在游戏中!"
  bedtargeting: "请指定或站在要配置的目标方块上!"
  regionargument: "地点必须是 'loc1'或 'loc2'!"
  spawnerargument: "资源参数必须是一个有效的配置!"
  blockdownnotfound: "在你所站的地方没有方块！"
  gamenotrunning: "游戏不能运行!"
  bungeenoserver: "Bungeecord服务器设置不正确！请联系管理员！"
  cantstartagain: "游戏运行中! 你不能再次开始这个游戏!"
  startoutofwaiting: "游戏已在等待时开始！"
  cantjoingame: "你不能加入正在进行或已经开始的游戏！"
  lobbyongameworld: "大厅不能与游戏场地在同一个世界！"
  gamenotloaded: "游戏无法开始!"
  gameloaderror: "载入游戏'$game$'的时候出现一个错误!"
  regionnotfound: "区域文件不存在!"
  savesign: "不能创建一个新的牌子配置文件！"
  nogames: "游戏不存在!"
  notenoughress: "你没有足够的资源购买这个物品!"
  teamnameinuse: "队伍名字已经被使用!"
  minplayersmustnumber: "最小玩家数必须是一个数字!"
  toolongregionname: "区域名字最大15个字符!"
  notwhilegamerunning: "游戏运行前不能这么做!"
  notwhileingame: "你不能在一个已开始的游戏中这么做！"
  timeincorrect: "时间必须是一个数 (0 ... 23000)，或 'day' 来表示白天，'night'来表示夜晚！"
  minplayersnumeric: "最小玩家的数量必须是一个整数！"
  notinair: "你不在空中！"
  teamfull: "该队伍已满，请重新打开队伍选择菜单！"
  novalidmaterial: "方块类型不正确!"
  wrongvalueonoff: "参数错误! 使用 true，on或1 来表示打开 - 使用 false，off或0 来表示关闭！"
  playernotfound: "指定玩家不存在或不在线！"
  notingameforkick: "你只能在游戏中踢出玩家!"
  playernotingame: "指定的玩家不在此游戏中！"
  mustbeinlobbyworld: "你必须与游戏大厅处于同一个世界"
  addteamjoincancel: "队伍的添加已被取消！"
  entitynotcompatible: "该实体不能加入队伍！"
success:
  gameadded: "新游戏'$game$'成功添加!"
  teamadded: "成功添加队伍'$team$'！"
  joined: "你加入了游戏！"
  left: "你离开了游戏!"
  saved: "成功保存游戏！"
  bedset: "成功设置了$team$队的重生点！"
  regionset: "成功为$game$设置区域$location$!"
  spawnset: "成功设置了$team$队的出生点!"
  spawnerset: "$name$的资源刷新点添加成功!"
  stopped: "游戏停止!"
  lobbyset: "大厅设置成功!"
  gameloaded: "成功载入了游戏 '$game$'！"
  reloadconfig: "配置文件重载成功!"
  teamremoved: "队伍成功移除!"
  gameremoved: "游戏成功移除!"
  spawnercleared: "已移除所有的资源生成点！"
  gamerun: "你开始了游戏，玩家现在可以加入!"
  timeset: "游戏时间成功设置!"
  regionnameset: "区域名字设置成功!"
  minplayersset: "最小玩家设置成功!"
  mainlobbyset: "主大厅设置成功!"
  gametimeset: "游戏时间设置成功!"
  materialset: "重生点方块设置成功!"
  builderset: "地图作者已经成功设置，将会显示地图作者名字"
  autobalanceseton: "自动平衡已打开！"
  autobalancesetoff: "自动平衡已关闭！"
  selectteamjoinentity: "现在，右键选择用来加入队伍的实体！"
  teamjoinadded: "已成功标记了用以加入队伍$team$的实体"
  holoremoved: "成功移除了全息显示的计分板！"
gamecheck:
  LOC_NOT_SET_ERROR: "区域的位置没有设置正确!"
  TEAM_SIZE_LOW_ERROR: "你需要设置更多队伍!"
  NO_RES_SPAWNER_ERROR: "你没有设置任何资源刷新点!"
  NO_LOBBY_SET: "你没有设置大厅!"
  TEAMS_WITHOUT_SPAWNS: "至少有一个队伍没有设置出生点!"
  NO_ITEMSHOP_CATEGORIES: "商店内没有物品!"
  NO_MAIN_LOBBY_SET: "你没有设置主大厅即使你设置'tomainlobby' 为true"
  TEAM_NO_WRONG_BED: "有队伍没有设置床!"
  TEAM_NO_WRONG_TARGET: "有队伍没有设置床!"
ingame:
  team: "Team"
  teams: "队伍"
  all: "全部"
  record: "&e$record$&a 是这个地图的记录!"
  record-with-holders: '&a这个地图的记录是&e$record$&a记录者: $holders$'
  newrecord: '&a$team$&a队设置一个新的记录: &6$record$'
  record-nobeddestroy: "&c记录不能保存, 因为没有床被摧毁!"
  teamwon: "恭喜 ！$team$队获得胜利!"
  draw: "游戏以平局结束！"
  serverrestart: "服务器将在$sec$ 秒后重置地图!"
  gamestarting: "游戏开始 ..."
  gamestarted: "游戏 '$game$' 刚刚开始!"
  backtolobby: "将在 $sec$秒后返回大厅!"
  spectator: "旁观者"
  spectate: "&a旁观"
  teamchest: "队伍箱子"
  noturteamchest: "这个箱子不属于你的队伍！"
  protectionleft: "将会有&c$length$&f秒无敌时间!"
  protectionend: "无敌时间结束，小心行事!"
  team-dead: "队伍$team$被消灭!"
  no-friendlybreak: "&chi，不能破坏队伍成员脚下的方块!"
  teamchestdestroy: "&c有一个队伍箱子被破坏!"
  title:
    map-builder: "地图作者$builder$"
    win-title: "&6恭喜!"
    win-subtitle: "$team$&6队获得胜利，用时&e$time$"
  shop:
    name: "商店"
    newshop: "使用新商店"
    oldshop: "使用旧商店"
    fullstackpershift: "按住Shift点击获取多组物品"
    onestackpershift: "按住Shift点击获取一组物品"
  player:
    left: "玩家 $player$ 离开了游戏!"
    died: "$player$死了!"
    killed: "$killer$杀死了 $player$!"
    kicked: "$player$ 被踢出!"
    waskicked: "你从这个游戏被踢出!"
  blocks:
    ownbeddestroy: "你不能破坏自己的床!"
    beddestroyed: "$player$破坏了 $team$的床！"
  specials:
    rescue-platform:
      left: "需要&c$time$秒&f 你才能使用下一个救援平台!"
    arrow-blocker:
      start: "箭矢伤害保护开启，剩余时间 &3 $time$ &f秒(s)"
      end: "箭矢伤害保护时间已结束"
      left: "箭矢保护冷却中，剩余时间 &3$time$ &f秒(s)"
    trap:
      trapped: "&e有人进入了你队伍的&4陷阱"
    protection-wall:
      left: "需要&c$time$&f 秒你才能使用下一个保护墙!"
      not-usable-here: "你不能在这里使用保护墙!"
    warp-powder:
      cancelled: "&c你的传送被取消!"
      start: "&c$time$&f 秒后你将被传送，请不要移动!"
      cancel: "&4取消传送"
      multiuse: "&c你已经开始了一个传送!"
    tntsheep:
      no-target-found: "没有找到目标玩家!"
    tracker:
      no-target-found: "没有找到目标玩家!"
      target-found: "$player$ 是$blocks$方块远离你."
lobby:
  countdown: "游戏将在$sec$ 秒后开始!"
  countdowncancel: "需要更多玩家，倒计时被取消!"
  cancelcountdown:
    not_enough_players: "需要更多玩家，倒计时被取消!"
    not_enough_teams: "需要更多的队伍，倒计时取消。"
  cancelstart:
    not_enough_players: "开始游戏需要更多的玩家!"
    not_enough_teams: "需要更多的游戏才能开始游戏。"
  chooseteam: "选择一个队伍"
  startgame: "开始游戏"
  reduce_countdown: "倒计时减少"
  gamefull: "游戏已满!"
  gamefullpremium: "此游戏玩家已满!"
  teamjoined: "你成功加入了 $team$队"
  leavegame: "离开游戏"
  playerjoin: "$player$ 加入了游戏!"
  kickedbyvip: "你被vip玩家从这个游戏房间踢出!"
  moreplayersneeded: "需要$count$ 个玩家."
  moreplayersneeded-one: "需要$count$个玩家."
  moreteamsneeded: "至少需要有两个玩家并且处于不同的队伍才能开始游戏!"
sign:
  firstline: "&6[护床战争]"
  players: "玩家"
  gamestate:
    stopped: "&4停止!"
    waiting: "&a等待中 ..."
    running: "&9正在运行!"
    full: "已满!"
stats:
  header: "护床战争统计"
  kd: "击杀/死亡"
  statsnotfound: "没有找到 $player$ 的游戏统计信息!"
  name: "名字"
  kills: "击杀"
  deaths: "死亡"
  wins: "获胜"
  loses: "失败"
  score: "分数"
  destroyedBeds: "破坏床"
  games: "游戏次数"
commands:
  addgame:
    name: "添加游戏"
    desc: "添加一个新游戏"
  addteam:
    name: "添加队伍"
    desc: "添加一个队伍到指定游戏"
  join:
    name: "加入游戏"
    desc: "加入指定游戏"
  leave:
    name: "离开游戏"
    desc: "离开当前游戏"
  save:
    name: "保存游戏"
    desc: "保存游戏地图和配置文件"
  settarget:
    name: "设置陷进"
    desc: "设置一个目标陷进"
  setbed:
    name: "设置床 (synonym for settarget)"
    desc: "为队伍设置床 (Synonym for settarget)"
  setlobby:
    name: "设置大厅"
    desc: "设置一个游戏大厅"
  setregion:
    name: "设置区域点"
    desc: "为指定游戏设置游戏区域点"
  setspawn:
    name: "设置队伍出生点"
    desc: "为队伍设置出生点"
  setspawner:
    name: "设置资源刷新点"
    desc: "设置指定的资源刷新点"
  start:
    name: "开始游戏"
    desc: "开始游戏"
  stop:
    name: "停止游戏"
    desc: "停止游戏"
  help:
    name: "显示帮助"
    desc: "显示关于这个插件的所有指令"
  reload:
    name: "重载"
    desc: "重新载入配置文件和语言文件"
  setmainlobby:
    name: "设置主大厅"
    desc: "设置游戏主大厅 (is needed when mainlobby-enabled is set to true)"
  list:
    name: "游戏列表"
    desc: "列出可用的游戏"
  regionname:
    name: "设置区域名字"
    desc: "给区域设置单独的名字 (instead of world name)"
  removeteam:
    name: "移除多雾"
    desc: "从游戏移除一个队伍 (only in stopped mode)"
  removegame:
    name: "移除游戏"
    desc: "从配置文件删除游戏"
  clearspawner:
    name: "清除资源刷新点"
    desc: "移除所有刷新点，需要保存."
  gametime:
    name: "设置游戏时间"
    desc: "设置游戏时间"
  stats:
    name: "统计"
    desc: "显示你的统计"
  setbuilder:
    name: "设置地图作者"
    desc: "设置地图作者，将会在游戏结束的时候显示."
  setgameblock:
    name: "设置游戏方块"
    desc: "设置游戏的方块类型将会替代配置文件里面的 'game-block' . 输入'DEFAULT' 使用默认设置"
  setautobalance:
    name: "设置自动排行"
    desc: "如果'global-autobalance'设置为'false', 使用这个指令将会设置为'true'!"
  setminplayers:
    name: "设置最小玩家数"
    desc: "设置游戏开始时需要的玩家数量"
  kick:
    name: "踢出玩家"
    desc: "将指定玩家从当前游戏踢出!"
  addteamjoin:
    name: "添加队伍选择"
    desc: "在当前位置加入指定队伍的标记!"
  addholo:
    name: "添加全息显示"
    desc: "在当前位置将增加全息图统计!"
  removeholo:
    name: "删除权限显示"
    desc: "使用这个命令后玩家right-click即可移除全息图."
    explain: "10秒只可执行一次"
  debugpaste:
    name: "Paste debug data"
    desc: "这将向 hastebin 计划发送一些调试数据，并返回一个连接以提供给开发人员"
  itemspaste:
    name: "Paste your inventory to a file"
    desc: "This will return a link where you can see your current inventory being serialized as an example for your shop.yml"
