---
default:
  pages: "Страница: $current$ из $max$"
  currently: Сейчас
errors:
  argumentslength: "Количество аргументов не соответствует правильному количеству!"
  holodependencynotfound: "Не удалось найти $dependency$ для статистики голограммы"
  packagenotfound: "Не удалось получить $package$ пакет"
  classnotfound: "Не удалось получить пакет $package$ для класса $class$"
  gameexists: "Игра с таким названием уже существует!"
  gamenotfound: "Игра '$game$' не найдена!"
  nofreegames: "Нет доступных свободных игр."
  gamenotfoundsimple: "Игра не найдена!"
  playeramount: "Максимальное кол-во игроков не может быть ниже 1 и выше 24!"
  teamcolornotallowed: "Данный командный цвет не допускается"
  teamnamelength: "Название команды должно быть от 2 до 20 символов!"
  teamnotfound: "Команда не найдена!"
  notingame: "В настоящее время вы вне игры!"
  bedtargeting: "Вы должны стоять на блоке который вы указали в конфиге вместо кровати"
  regionargument: "Допустимые аргументы: 'loc1' или 'loc2'"
  spawnerargument: "Параметр ресурса должен быть допустимым настроенным ресурсом!"
  blockdownnotfound: "Вы должны стоять на блоке"
  gamenotrunning: "Игра не запущена!"
  bungeenoserver: "Bungeecord сервер не был установлен должным образом. Сообщите проблему Администрации"
  cantstartagain: "Игра уже запущена! Вы не можете запустить её ещё раз!"
  startoutofwaiting: "Игра уже запущена в режиме ожидания!"
  cantjoingame: "Вы не можете присоеденится к запущеной игре"
  lobbyongameworld: "Лобби не может быть в игровом мире"
  gamenotloaded: "Не удалось запустить игру"
  gameloaderror: "Загружаемая игра '$game$' выдает ошибку"
  regionnotfound: "Файл с регионами не существует"
  savesign: "Не удалось создать новый файл конфигурации"
  nogames: "Игры не найдены"
  notenoughress: "У вас не хватает ресурсов чтобы купить этот предмет"
  teamnameinuse: "Название команды уже используется"
  minplayersmustnumber: "Минимальное количество игроков должно быть не ниже 2"
  toolongregionname: "Максимальная длина имени региона - 15 символов"
  notwhilegamerunning: "Вы не можеет сделать это во время игры"
  notwhileingame: "Вы не можеет сделать это во время игры"
  timeincorrect: "Время должно быть числом (0 ... 23000), 'днём' или 'ночью'"
  minplayersnumeric: "Минимальное количество игроков должно быть не ниже 2"
  notinair: "Вы не в воздухе"
  teamfull: "В этой команде уже нет свободного места"
  novalidmaterial: "Данный тип блока (материала) является неправильной!"
  wrongvalueonoff: "Неправильный параметр! Используйте true,on,1, чтобы включить - используйте false,off,0, чтобы выключить!"
  playernotfound: "Данный игрок не был найден или он не в сети!"
  notingameforkick: "Вы должны быть в игре, чтобы ударить игрока!"
  playernotingame: "Данного игрока нет в этой игре!"
  mustbeinlobbyworld: "You must be in the lobby world of the game!"
  addteamjoincancel: "Добавление команды join был отменён!"
  entitynotcompatible: "Эта сущность не совместима с командой join!"
success:
  gameadded: "Новая игра '$game$' успешно создана"
  teamadded: "Команда '$team$' успешно добавлена"
  joined: "Вы присоединились к игре"
  left: "Вы отсоединились от игры"
  saved: "Игра успешно сохранена!"
  bedset: "Вы успешно установили точку для кровати команды $team$"
  regionset: "Игровая локация $location$ успешно добавлена для игры $game$!"
  spawnset: "Точка возрождения успешно добалена для команды $team$"
  spawnerset: "Спавнер ресурса $name$ успешно добавлен"
  stopped: "Игра успешно остановлена!"
  lobbyset: "Лобби успешно добавлено"
  gameloaded: "Игра '$game$' успешно загружена!"
  reloadconfig: "Перезагрузка прошла успешно"
  teamremoved: "Команда успешно удалена"
  gameremoved: "Игра успешно удалена!"
  spawnercleared: "Все спавнеры ресурсов были удалены"
  gamerun: "Вы запустили игру. Теперь игроки могут подключатся"
  timeset: "Игровое время успешно установлено!"
  regionnameset: "Имя региона изменено"
  minplayersset: "Минимальное к-во игроков изменено"
  mainlobbyset: "Главное лобби успешно добавлено"
  gametimeset: "Игровое время установлено!"
  materialset: "Тип блока респауна (материала) была успешно установлена!"
  builderset: "Строитель для карты была успешно установлена и будет отображаться в названии!"
  autobalanceseton: "Автобаланс был успешно включён!"
  autobalancesetoff: "Автобаланс был успешно &cотключён&a!"
  selectteamjoinentity: "Теперь сделайте правый клик на моба, который требуется использовать в качестве команды join!"
  teamjoinadded: "Моб был успешно отмечен как выбор команды для команды $team$"
  holoremoved: "Статистика голограммы успешно удалена!"
gamecheck:
  LOC_NOT_SET_ERROR: "Места для региона небыли установлены правельно"
  TEAM_SIZE_LOW_ERROR: "Вы должны установить больше команд"
  NO_RES_SPAWNER_ERROR: "Вы не установили ни одного спавнера ресурсов"
  NO_LOBBY_SET: "Вы не установили лобби"
  TEAMS_WITHOUT_SPAWNS: "Обнаружена(ы) команда(ы) без точки возрождения"
  NO_ITEMSHOP_CATEGORIES: "Данная категория не найдена"
  NO_MAIN_LOBBY_SET: "Вы не установили точку главного лобби, хотя включили 'tomainlobby'"
  TEAM_NO_WRONG_BED: "Обнаружена(ы) команда(ы) без кровати"
  TEAM_NO_WRONG_TARGET: "Одна или больше команд не имеет кровати"
ingame:
  team: "Команда"
  teams: "Команде"
  all: "Всем"
  record: "&aЖелаем &cВам &bприятной &eигры"
  record-with-holders: '&aЖелаем &cВам &bприятной &eигры'
  newrecord: '&aЖелаем &cВам &bприятной &eигры'
  record-nobeddestroy: "&aЖелаем &cВам &bприятной &eигры"
  teamwon: "Поздравляем! Команда $team$ победила"
  draw: "Ничья"
  serverrestart: "Перезагрузка сервера через $sec$"
  gamestarting: "Игра начинается"
  gamestarted: "Игра началась"
  backtolobby: "Вы телепортируетесь в лобби через $sec$"
  spectator: "Зритель"
  spectate: "&aСледить"
  teamchest: "Сундук команды: $team$"
  noturteamchest: "Этот сундук для вашей команды"
  protectionleft: "&cНеуязвимость &fзаканчивается через &c$length$"
  protectionend: "Теперь вы снова &cуязвимы"
  team-dead: "Команда $team$ полностью уничтожена"
  no-friendlybreak: "&cНе ломайте блок под членом команды!"
  teamchestdestroy: "&cОдин из ваших сундуков команды был уничтожен!"
  title:
    map-builder: "Построил $builder$"
    win-title: "&6Поздравляем!"
    win-subtitle: "Команда $team$ &6выиграла за &e$time$"
  shop:
    name: "Магазин Ресурсов"
    newshop: "Использовать новую механику покупки"
    oldshop: "Использовать старую механику покупки"
    fullstackpershift: "Купить много стаков при ЛКМ+Shift"
    onestackpershift: "Купить один стак при ЛКМ+Shift"
  player:
    left: "Игрок $player$ покинул игру"
    died: "$player$ умер"
    killed: "$killer$ убил $player$"
    kicked: "$player$ был выгнан!"
    waskicked: "Вы были выгнаны из игры!"
  blocks:
    ownbeddestroy: "Вы не можете разрушить кровать своей команды"
    beddestroyed: "$player$ разрушил кровать команды $team$"
  specials:
    rescue-platform:
      left: "Платформа исчезнет через &c$time$&f"
    arrow-blocker:
      start: "Вы защищены от удара стрелы на &c$time$&f сек."
      end: "&cВаша защита от стрел закончилась"
      left: "Осталось &c$time$&f секунд(ы), когда вы можете использовать следующую блокировку стрел!"
    trap:
      trapped: "&eКто-то попался в ловушку вашей команды"
    protection-wall:
      left: "Новую стену можно построить через &c$time$"
      not-usable-here: "Вы не можете построить стену здесь"
    warp-powder:
      cancelled: "&cТелепортация отменена"
      start: "Вы будете телепортированы через &c$time$&f second(s). Не двигайтесь с места"
      cancel: "&4Отменить телепортацию"
      multiuse: "&cВы уже телепортировались!"
    tntsheep:
      no-target-found: "Игрок не был найден!"
    tracker:
      no-target-found: "Игрок не был найден!"
      target-found: "$player$ имеет $blocks$ блок(ов) вдалеке от вас."
lobby:
  countdown: "Игра начнётся через $sec$"
  countdowncancel: "Нужно больше игроков. Отчёт времени отменён"
  cancelcountdown:
    not_enough_players: "Нужно больше игроков. Отчёт времени отменён"
    not_enough_teams: "Необходимо больше команд. Обратный отсчёт отменён!"
  cancelstart:
    not_enough_players: "Нужно больше игроков для начала игры"
    not_enough_teams: "Чтобы начать игру, необходимы дополнительные группы!"
  chooseteam: "Выберите команду:"
  startgame: "Начать игру"
  reduce_countdown: "Уменьшить обратный отсчёт"
  gamefull: "Игра заполнена!"
  gamefullpremium: "Игра уже полна премиум-игроков!"
  teamjoined: "Вы вступили в команду $team$"
  leavegame: "Покинуть игру"
  playerjoin: "Игрок $player$ присоеденился к игре"
  kickedbyvip: "Вы были кикнуты из-за вип игрока, который присоеденился в заполненую игру"
  moreplayersneeded: "Необходимо ещё $count$ игроков."
  moreplayersneeded-one: "Необходим ещё $count$ игрок."
  moreteamsneeded: "Необходимо как минимум два игрока в двух разных командах, чтобы начать игру!"
sign:
  firstline: "&e[&bBedWars&e]"
  players: "Игроков"
  gamestate:
    stopped: "&4Остановлена"
    waiting: "&2Ожидание"
    running: "&bЗапущена"
    full: "&6Заполнена"
stats:
  header: "Статистика:"
  kd: "К/Д"
  statsnotfound: "Статистика игрока $player$ не найдена!"
  name: "Имя"
  kills: "Убийств"
  deaths: "Смертей"
  wins: "Побед"
  loses: "Поражений"
  score: "Результат"
  destroyedBeds: "Разрушено кроватей"
  games: "Сыграно игр"
commands:
  addgame:
    name: "Добавить игру"
    desc: "создать новую игру"
  addteam:
    name: "Добавить команду"
    desc: "добавить команду в игру"
  join:
    name: "Зайти в игру"
    desc: "присоеденится к игре"
  leave:
    name: "Покинуть игру"
    desc: "покинуть игру"
  save:
    name: "Сохранить игру"
    desc: "сохранить игру в config.yml"
  settarget:
    name: "Установить цель"
    desc: "установить кровать для команды"
  setbed:
    name: "Set bed"
    desc: "установить кровать для команды"
  setlobby:
    name: "Установить лобби"
    desc: "установить локацию для лобби"
  setregion:
    name: "Устанавливает точку региона"
    desc: "установить метки для региона под игру"
  setspawn:
    name: "Устанавливает спаун команды"
    desc: "установить точку возрождения команды"
  setspawner:
    name: "Установить спаунер"
    desc: "установить спавнер ресурсов"
  start:
    name: "Начать игру"
    desc: "начать игру"
  stop:
    name: "Остановить игру"
    desc: "остановить игру"
  help:
    name: "Показать справку"
    desc: "посмотреть информацию о плагине"
  reload:
    name: "Перезагрузить"
    desc: "перезагрузить конфиг"
  setmainlobby:
    name: "Установить главную лобби"
    desc: "установить локацию для главного лобби"
  list:
    name: "Список игр"
    desc: "список доступных игр"
  regionname:
    name: "Установить название региона"
    desc: "задать имя для региона"
  removeteam:
    name: "Удалить команду"
    desc: "удалить команду из игры"
  removegame:
    name: "Удалить игру"
    desc: "удалить игру и её настройки"
  clearspawner:
    name: "Очистить спаунеры"
    desc: "удалить все спавнеры предметов"
  gametime:
    name: "Установка игрового времени"
    desc: "установить тип игры"
  stats:
    name: "Статистика"
    desc: "показать вашу статистику"
  setbuilder:
    name: "Устанавливает имя строителя карты"
    desc: "Устанавливает строителя карты, которая будет отображаться в заголовке, когда игра начинается."
  setgameblock:
    name: "Установить блок"
    desc: "Устанавливает тип игры блока для этой игры, которую следует использовать вместо настройки \"игровой блок\". Запись \"ПО УМОЛЧАНИЮ\" как тип, чтобы снова использовать тип настроек"
  setautobalance:
    name: "Набор автобаланса"
    desc: "Если глобальный автобаланс имеет значение \"false\", с этой командой. вы можете установить автобаланс команды или отключить!"
  setminplayers:
    name: "Установить минимальное число игроков"
    desc: "Устанавливает количество игроков, необходимое для начала игры"
  kick:
    name: "Выгнать игрока"
    desc: "Выгнать игрока из текущей игры!"
  addteamjoin:
    name: "Добавить выбор команды"
    desc: "Отмеченный моб, который может использоваться для конкретной команды!"
  addholo:
    name: "Добавить расположение голограммы"
    desc: "Статистика голограммы будет добавлена в текущей позиции!"
  removeholo:
    name: "Удалить расположение голограммы"
    desc: "Когда была выполнена команда, игрок может нажать на правую кнопку мыши голограммы, которая должна быть удалена."
    explain: "Выполните левый клик в течение 10 секунд на голограмме, которую вы хотите удалить."
  debugpaste:
    name: "Вставить данные отладки"
    desc: "Это отправляет данные для отладки в hastebin и предоставляет ссылку, которую вы можете отправить разработчикам"
  itemspaste:
    name: "Записать содержимое инвентаря в файл"
    desc: "Вы получите ссылку, где можно будет посмотреть предметы вашего инвентаря в виде примера для конфигурации shop.yml"
