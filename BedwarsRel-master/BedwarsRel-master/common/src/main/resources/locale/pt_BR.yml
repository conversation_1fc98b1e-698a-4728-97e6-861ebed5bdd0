---
default:
  pages: "Página $current$ de $max$"
  currently: Atualmente
errors:
  argumentslength: "O número de argumentos não é válido!"
  holodependencynotfound: "Não foi encontado o $dependency$ para as estatísticas nos hologramas"
  packagenotfound: "Não foi possível obter o pacote $package$!"
  classnotfound: "Não foi possível encontar $package$ na classe $class$!"
  gameexists: "Uma sala já possui este nome!"
  gamenotfound: "A sala '$game$' não foi encontrada!"
  nofreegames: "Nenhuma partida iniciada."
  gamenotfoundsimple: "Sala desconhecida!"
  playeramount: "O máximo de jogadores não pode ser inferior a 1 ou superior a 24!"
  teamcolornotallowed: "A cor para a equipe colocada não é válida"
  teamnamelength: "O nome da equipe deve ter entre 2 e 20 caracteres!"
  teamnotfound: "Time não encontrado!"
  notingame: "Você não está em uma sala!"
  bedtargeting: "Você deve olhar ou ficar sobre um 'bloco da partida'!"
  regionargument: "A localização dada deve ser 'loc1' ou 'loc2'!"
  spawnerargument: "O recurso usado deve estar configurado!"
  blockdownnotfound: "O bloco em que você está parado não foi encontrado!"
  gamenotrunning: "A partida não está iniciada!"
  bungeenoserver: "Servidores Bungeecord não foram definidos corretamente. Avise um membro da equipe!"
  cantstartagain: "A partida já começou. Não é possível iniciar novamente!"
  startoutofwaiting: "O jogo precisa ser iniciado no modo de espera!"
  cantjoingame: "Você não pode participar de um jogo iniciado ou cancelado!"
  lobbyongameworld: "O lobby não pode estar no mundo das partidas!"
  gamenotloaded: "Não foi possível iniciar o jogo!"
  gameloaderror: "Enquanto carregava o jogo '$game$' ocorreu um erro!"
  regionnotfound: "Região dada não existe!"
  savesign: "Não foi possível criar um novo arquivo de configuração de placas!"
  nogames: "Sala/partida desconhecida!"
  notenoughress: "Você não possui coins suficientes para comprar!"
  teamnameinuse: "Nome do time já está sendo usado!"
  minplayersmustnumber: "O mínimo de jogadores deve ser um número!"
  toolongregionname: "O tamanho do nome da região é de no máximo 15 caracteres!"
  notwhilegamerunning: "Não é possível fazer isso enquanto joga!"
  notwhileingame: "Não é possível fazer isso enquanto está numa partida!"
  timeincorrect: "O tempo deve ser um número (de 0 até 23000) ou 'day'/'night'!"
  minplayersnumeric: "O mínimo de jogadores deve ser um número!"
  notinair: "Você não está no ar!"
  teamfull: "Este time está cheio, escolha outro no menu!"
  novalidmaterial: "O tipo de bloco (item) dado não está correto!"
  wrongvalueonoff: "Parâmetro incorreto! Use o 'true' para ligar ou 'false' para desativar!"
  playernotfound: "Jogador não encontrado!"
  notingameforkick: "Você deve estar na partida para kickar o jogador!"
  playernotingame: "Jogador não está em uma partida!"
  mustbeinlobbyworld: "Você deve estar no mundo do lobby do jogo"
  addteamjoincancel: "A adição da equipe foi cancelada!"
  entitynotcompatible: "Esta entidade não é compatível com a equipe!"
success:
  gameadded: "Nova sala '$game$' adicionada com sucesso!"
  teamadded: "Time '$team$' adicionado com sucesso!"
  joined: "Você entrou na sala!"
  left: "Você saiu da sala!"
  saved: "Sala salva com sucesso!"
  bedset: "Definido com sucesso o bloco de respawn do time $team$!"
  regionset: "Área do jogo $game$ definido para $location$!"
  spawnset: "Área de spawn do time $team$ definida!"
  spawnerset: "Forja de recursos $name$ definida com sucesso!"
  stopped: "Partida cancelada!"
  lobbyset: "Lobby definido com sucesso!"
  gameloaded: "Sala '$game$' carregada com sucesso!"
  reloadconfig: "Sistema do Bedwars recarregado!"
  teamremoved: "O time foi removido com sucesso!"
  gameremoved: "A sala foi removida com sucesso!"
  spawnercleared: "Todas forjas de recursos removida com sucesso!"
  gamerun: "Você iniciou uma sala, jogadores podem entrar!"
  timeset: "Tempo de jogo foi definido com sucesso!"
  regionnameset: "Nome da área definido com sucesso!"
  minplayersset: "O mínimo de jogadores definido!"
  mainlobbyset: "O Lobby principal foi definido!"
  gametimeset: "O tempo do jogo foi definido com sucesso!"
  materialset: "O tipo do bloco de respawn (material) foi definido!"
  builderset: "O construtor do mapa foi definido e vai aperecer no título do jogo!"
  autobalanceseton: "Saldo automático foi ativado com sucesso!"
  autobalancesetoff: "Saldo automático agora de &coff&a!"
  selectteamjoinentity: "Clique com o botão direito no npc para escolher o time!"
  teamjoinadded: "Entidade definida como seleção de equipe para o time $team$"
  holoremoved: "Holograma de estatísticas removido!"
gamecheck:
  LOC_NOT_SET_ERROR: "Locais para a região não foram definidas corretamente!"
  TEAM_SIZE_LOW_ERROR: "Você tem que definir mais times!"
  NO_RES_SPAWNER_ERROR: "Você não definiu a forja de recursos!"
  NO_LOBBY_SET: "Você não definiu um lobby!"
  TEAMS_WITHOUT_SPAWNS: "Alguns respawns de times não foram definidos!"
  NO_ITEMSHOP_CATEGORIES: "Nenhuma categoria da loja de itens encontrada!"
  NO_MAIN_LOBBY_SET: "Você não definir um lobby principal, mesmo que você definiu para true o 'tomainlobby'"
  TEAM_NO_WRONG_BED: "Uma ou mais times não tem cama(s) definida(s)!"
  TEAM_NO_WRONG_TARGET: "Um ou mais times não tem nenhum bloco de respawn definido!"
ingame:
  team: "Time"
  teams: "Times"
  all: "Todos"
  record: "&e$record$&a foi o tempo recorde deste mapa!"
  record-with-holders: '&aO recorde deste mapa &e$record$&a e foi feito por: $holders$'
  newrecord: '&aO time $team$&a realizou um novo recorde: &6$record$ Parabéns'
  record-nobeddestroy: "&cRecorde não será salvo, porque a cama não foi destruída!"
  teamwon: "Parabéns ao vencedor: Time $team$!"
  draw: "A partida acabou empatado!"
  serverrestart: "O servidor irá reiniciar em $sec$ segundos(s)!"
  gamestarting: "Partida iniciando..."
  gamestarted: "A partida '$game$' acabou de começar!"
  backtolobby: "Retornando ao lobby em $sec$ segundo(s)!"
  spectator: "Espectador"
  spectate: "&aEspectador"
  teamchest: "Baú do time"
  noturteamchest: "Este baú não é de sua equipe!"
  protectionleft: "Invulnerável por &c$length$&f segundo(s)!"
  protectionend: "Agora você está &cvulnerable&f novamente!"
  team-dead: "Time $team$ teve sua cama destruída!"
  no-friendlybreak: "&cNão é possível quebrar este bloco!"
  teamchestdestroy: "Um dos seus membros da equipe foi eliminado!"
  title:
    map-builder: "Construído por $builder$"
    win-title: "&6Parabéns!"
    win-subtitle: "Time $team$&6 ganhou em &c$time$"
  shop:
    name: "Loja de itens"
    newshop: "Use a nova loja"
    oldshop: "Use a antiga loja"
    fullstackpershift: "Para comprar o dobro, clique com shift"
    onestackpershift: "Para comprar 64 deste item, clique com shift"
  player:
    left: "$player$ saiu da partida!"
    died: "$player$ morreu sozinho!"
    killed: "$killer$ foi morto por $player$!"
    kicked: "$player$ foi removido da partida!"
    waskicked: "Você foi removido da partida!"
  blocks:
    ownbeddestroy: "Hey! Não é possível quebrar sua cama!"
    beddestroyed: "$player$ destruiu a cama do time $team$!"
  specials:
    rescue-platform:
      left: "O time &c$time$&f possui alguns segundos para retornar!"
    arrow-blocker:
      start: "Você está protegido de ser atingido por flechas por &c$time$&f segundo(s)."
      end: "&cSua proteção contra flechas acabou"
      left: "O time &c$time$&f possui alguns segundos para usar o próximo anti-flechas!"
    trap:
      trapped: "&eAlguém caiu em sua armadilha &ctrap&e!"
    protection-wall:
      left: "Existem &c$time$&f segundo(s) para que você possa usar a parede de proteção!"
      not-usable-here: "Você não pode usar a parede de proteção aqui!"
    warp-powder:
      cancelled: "&cSeu teletransporte foi cancelado!"
      start: "Você será teleportado em &c$time$&f segundo(s). Não se mexa!"
      cancel: "&4Calcele o teleporte"
      multiuse: "&cYou já começou um teletransporte!"
    tntsheep:
      no-target-found: "Nenhum jogador alvo foi encontrado!"
    tracker:
      no-target-found: "Nenhum jogador alvo foi encontrado!"
      target-found: "$player$ está a $blocks$ blocos de você."
lobby:
  countdown: "A partida iniciará em $sec$ segundo(s)!"
  countdowncancel: "São necessários mais jogadores, contador cancelado!"
  cancelcountdown:
    not_enough_players: "São necessários mais jogadores, contador cancelado!"
    not_enough_teams: "São necessários mais times, contador cancelado!"
  cancelstart:
    not_enough_players: "São necessários mais jogadores para a partida iniciar!"
    not_enough_teams: "São necessários mais times para a partida iniciar!"
  chooseteam: "Escolha o time"
  startgame: "Iniciar o jogo"
  reduce_countdown: "Reduzir a contagem regressiva"
  gamefull: "Sala cheia!"
  gamefullpremium: "O jogo já está cheio de jogadores premium!"
  teamjoined: "Você juntou-se com ao time $team$"
  leavegame: "Deixar sala"
  playerjoin: "O jogador $player$ entrou na sala!"
  kickedbyvip: "Você foi removido da sala para dar o lugar a um Vip!"
  moreplayersneeded: "$count$ jogadores necessários."
  moreplayersneeded-one: "$count$ jogadores necessários."
  moreteamsneeded: "Um mínimo de dois jogadores em dois times diferentes é necessário para iniciar o jogo!"
sign:
  firstline: "&6[Bedwars]"
  players: "Jogadores"
  gamestate:
    stopped: "&4Cancelado!"
    waiting: "&aAguardando..."
    running: "&9Em jogo!"
    full: "Lotado!"
stats:
  header: "Estatisticas do Bedwars"
  kd: "Kdr"
  statsnotfound: "Estatísticas para $player$ não foram encontradas!"
  name: "Nome"
  kills: "Abates"
  deaths: "Mortes"
  wins: "Vitórias"
  loses: "Perdeu"
  score: "Resultados"
  destroyedBeds: "Camas destruídas"
  games: "Jogos"
commands:
  addgame:
    name: "Adicionar o jogo"
    desc: "Adiciona um novo jogo"
  addteam:
    name: "Adiciona um time"
    desc: "Adiciona um novo time num determinado mapa"
  join:
    name: "Entre na sala"
    desc: "Junta-se um jogo específico"
  leave:
    name: "Deixar sala"
    desc: "Deixar o jogo atual"
  save:
    name: "Salvar jogo"
    desc: "Salva um jogo (e o mapa) nos arquivo(s)"
  settarget:
    name: "Definir o destino"
    desc: "Define a localização do bloco de destino de um time"
  setbed:
    name: "Define uma cama (ou respawn)"
    desc: "Define a localização do bloco de uma equipe cama (sinônimo para settarget)"
  setlobby:
    name: "Define o lobby"
    desc: "Define a localização do lobby na sala"
  setregion:
    name: "Define um ponto da região"
    desc: "Define um ponto da região para o jogo"
  setspawn:
    name: "Define um spawn de equipe"
    desc: "Define o spawn de um determinado time"
  setspawner:
    name: "Define o spawner"
    desc: "Define uma localização spawner de forja de recursos específica"
  start:
    name: "Iniciar o jogo"
    desc: "Começa o jogo"
  stop:
    name: "Para o jogo"
    desc: "Para um jogo"
  help:
    name: "Mostra ajuda"
    desc: "Da a informação sobre o plugin e seus comandos"
  reload:
    name: "Recarregar"
    desc: "Recarrega as configurações e traduções"
  setmainlobby:
    name: "Define o lobby principal"
    desc: "Define o lobby principal de um jogo (é necessário que deixe mainlobby como true)"
  list:
    name: "Lista de salas"
    desc: "Lista de salas disponíveis"
  regionname:
    name: "Define o nome da região"
    desc: "Define um nome de região individual (em vez do nome do mundo)"
  removeteam:
    name: "Remove um time"
    desc: "Remove uma equipe de jogo (apenas no modo parado)"
  removegame:
    name: "Remove uma sala"
    desc: "Remove uma sala e todas suas configurações"
  clearspawner:
    name: "Limpa os spawners"
    desc: "Remove todos os spawners da sala. Salvar será necessário."
  gametime:
    name: "Define o tempo de partida"
    desc: "Define o tempo de jogo que deve ser usado em todas salas"
  stats:
    name: "Estatísticas"
    desc: "Mostra as suas estatísticas"
  setbuilder:
    name: "Define o construtor do mapa"
    desc: "Define o construtor do mapa, que estará em exibição no título quando a partida começa."
  setgameblock:
    name: "Define o bloco de jogo"
    desc: "Define o tipo de bloco de jogo para esta partida que deve ser usado em vez da configuração de 'game-block'. Escreva 'DEFAULT' para usar o tipo padrão novamente"
  setautobalance:
    name: "Define saldo automático"
    desc: "Se 'global-autobalance' é definido como 'false', com este comando que você pode definir o saldo para o time por partida para ligar ou desligar!"
  setminplayers:
    name: "Definie o mínimo de jogadores"
    desc: "Define a quantidade de jogadores necessários para iniciar uma partida"
  kick:
    name: "Remove/kicka um jogador"
    desc: "Remove/kicka um jogador da partida atual!"
  addteamjoin:
    name: "Adiciona a seleção de times"
    desc: "Marca uma criatura que pode ser usada para participar de um time específico!"
  addholo:
    name: "Adiciona na localização um holograma"
    desc: "Um holograma de estatísticas será adicionado na posição atual!"
  removeholo:
    name: "Remove na localização o holograma"
    desc: "Quando o comando for executado, o jogador pode clicar com o botão direito no holograma que será removido."
    explain: "Execute um clique esquerdo em 10 segundos no holograma que deseja remover."
  debugpaste:
    name: "Colar dados de depuração"
    desc: "Isto irá enviar alguns dados de debug para hastebin e retorna um link que você pode compartilhar com desenvolvedores"
  itemspaste:
    name: "Cole seu inventário para um arquivo"
    desc: "Isso retornará um link onde você pode ver seu inventário atual que será usado como um exemplo para seu shop.yml"
