### SHOP ###
### NOT ALLOWED CATEGORY MATERIAL: SLIME_BALL, SNOW_BALL, BUCKE<PERSON>, LAVA_BUCKET
shop:
  armor:
    item: DIAMOND_CHESTPLATE
    name: "护甲"
    order: 10
    permission: bw.base
    lore:
    - "用护甲保护自己！点击这里。"
    offers:
    - price:
      - type: CLAY_BRICK
      reward:
      - type: LEATHER_HELMET
        meta:
          ==: ItemMeta
          meta-type: LEATHER_ARMOR
          enchants:
            PROTECTION_ENVIRONMENTAL: 1
    - price:
      - type: CLAY_BRICK
      reward:
      - type: LEATHER_LEGGINGS
        meta:
          ==: ItemMeta
          meta-type: LEATHER_ARMOR
          enchants:
            PROTECTION_ENVIRONMENTAL: 1
    - price:
      - type: CLAY_BRICK
      reward:
      - type: LEATHER_BOOTS
        meta:
          ==: ItemMeta
          meta-type: LEATHER_ARMOR
          enchants:
            PROTECTION_ENVIRONMENTAL: 1
    - price:
      - type: IRON_INGOT
        amount: 6
      - type: GOLD_INGOT
      reward:
      - type: IRON_BOOTS
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          display-name: "磁力靴"
          lore:
          - "穿上这双鞋子，有75%的几率"
          - "免疫击退效果！"
    - price:
      - type: IRON_INGOT
      reward:
      - type: CHAINMAIL_CHESTPLATE
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            PROTECTION_ENVIRONMENTAL: 1
    - price:
      - type: IRON_INGOT
        amount: 3
      reward:
      - type: CHAINMAIL_CHESTPLATE
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            PROTECTION_ENVIRONMENTAL: 2
    - price:
      - type: IRON_INGOT
        amount: 7
      reward:
      - type: CHAINMAIL_CHESTPLATE
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            PROTECTION_ENVIRONMENTAL: 3
    - price:
      - type: GOLD_INGOT
        amount: 10
      reward:
      - type: IRON_HELMET
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          display-name: "头盔"
          enchants:
            PROTECTION_ENVIRONMENTAL: 2
  swords:
    item: DIAMOND_SWORD
    name: "武器"
    order: 20
    permission: bw.base
    lore:
    - "点击这里获取武器来攻击你的敌人"
    offers:
    - price:
      - type: CLAY_BRICK
        amount: 8
      reward:
      - type: STICK
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            KNOCKBACK: 1
    - price:
      - type: IRON_INGOT
      reward:
      - type: GOLD_SWORD
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            DAMAGE_ALL: 1
    - price:
      - type: IRON_INGOT
        amount: 3
      reward:
      - type: GOLD_SWORD
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            DAMAGE_ALL: 2
    - price:
      - type: IRON_INGOT
        amount: 7
      reward:
      - type: GOLD_SWORD
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            DAMAGE_ALL: 3
    - price:
      - type: GOLD_INGOT
        amount: 3
      reward:
      - type: IRON_SWORD
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            DAMAGE_ALL: 2
            KNOCKBACK: 1
    - price:
      - type: GOLD_INGOT
        amount: 15
      reward:
      - type: GOLD_AXE
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          display-name: "无限之斧"
          enchants:
            DAMAGE_ALL: 1
            DURABILITY: 1
            KNOCKBACK: 1
  bows:
    item: 261
    name: "弓箭"
    order: 30
    permission: bw.base
    lore:
    - "用这些精美的弓箭证明你的射击技巧"
    offers:
    - price:
      - type: GOLD_INGOT
        amount: 3
      reward:
      - type: BOW
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            ARROW_INFINITE: 1
    - price:
      - type: GOLD_INGOT
        amount: 7
      reward:
      - type: BOW
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            ARROW_DAMAGE: 1
            ARROW_INFINITE: 1
    - price:
      - type: GOLD_INGOT
        amount: 13
      reward:
      - type: BOW
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            ARROW_DAMAGE: 2
            ARROW_INFINITE: 1
    - price:
      - type: GOLD_INGOT
        amount: 16
      reward:
      - type: BOW
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            ARROW_FIRE: 1
            ARROW_INFINITE: 1
    - price:
      - type: GOLD_INGOT
        amount: 15
      reward:
      - type: BOW
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            ARROW_KNOCKBACK: 1
            ARROW_FIRE: 1
            ARROW_INFINITE: 1
    - price:
      - type: GOLD_INGOT
      reward:
      - type: ARROW
  eat:
    item: COOKED_PORKCHOP
    name: "食物"
    order: 40
    permission: bw.base
    lore:
    - "饿了吗？来点吃的吧。"
    offers:
    - price:
      - type: CLAY_BRICK
        amount: 4
      reward:
      - type: COOKED_PORKCHOP
        amount: 2
    - price:
      - type: IRON_INGOT
        amount: 2
      reward:
      - type: GOLDEN_APPLE
    - price:
      - type: GOLD_INGOT
        amount: 5
      reward:
      - type: BREAD
        amount: 10
  pickaxes:
    item: DIAMOND_PICKAXE
    name: "镐子"
    order: 50
    permission: bw.base
    lore:
    - "想要破坏别人的方块？使用这些镐子。"
    offers:
    - price:
      - type: CLAY_BRICK
        amount: 4
      reward:
      - type: IRON_PICKAXE
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            LOOT_BONUS_BLOCKS: 1
    - price:
      - type: IRON_INGOT
        amount: 2
      reward:
      - type: IRON_PICKAXE
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            LOOT_BONUS_BLOCKS: 2
    - price:
      - type: GOLD_INGOT
      reward:
      - type: IRON_PICKAXE
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          enchants:
            LOOT_BONUS_BLOCKS: 3
  blocks:
    item: SANDSTONE
    name: "方块"
    order: 60
    permission: bw.base
    lore:
    - "想要到达其他地方！用方块搭桥吧。"
    offers:
    - price:
      - type: CLAY_BRICK
      reward:
      - type: SANDSTONE
        damage: 2
        amount: 2
    - price:
      - type: CLAY_BRICK
        amount: 7
      reward:
      - type: ENDER_STONE
    - price:
      - type: IRON_INGOT
        amount: 3
      reward:
      - type: IRON_BLOCK
    - price:
      - type: CLAY_BRICK
        amount: 15
      reward:
      - type: GLOWSTONE
        amount: 4
    - price:
      - type: CLAY_BRICK
        amount: 4
      reward:
      - type: GLASS
  usefulstuff:
    item: TNT
    name: "实用物品"
    order: 70
    permission: bw.base
    lore:
    - "寻找实用的物品？这里有一些！"
    offers:
    - price:
      - type: IRON_INGOT
        amount: 3
      reward:
      - type: TNT
    - price:
      - type: GOLD_INGOT
      reward:
      - type: FLINT_AND_STEEL
    - price:
      - type: IRON_INGOT
      reward:
      - type: CHEST
    - price:
      - type: GOLD_INGOT
      reward:
      - type: ENDER_CHEST
    - price:
      - type: IRON_INGOT
        amount: 15
      reward:
      - type: BLAZE_ROD
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          display-name: "救援平台"
          lore:
          - "用救援平台保护自己"
          - "免于掉入虚空"
    - price:
      - type: IRON_INGOT
        amount: 3
      reward:
      - type: STRING
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          display-name: "陷阱"
          lore:
          - "当敌人踩到你的陷阱时会收到通知"
          - "敌人将无法正常移动。"
    - price:
      - type: CLAY_BRICK
        amount: 64
      reward:
      - type: BRICK
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          display-name: "保护墙"
          lore:
          - "使用保护墙可以立即"
          - "在你面前创建一堵墙！"
    - price:
      - type: IRON_INGOT
        amount: 7
      reward:
      - type: SULPHUR
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          display-name: "传送粉末"
          lore:
          - "使用这个粉末后，你将在6秒内"
          - "传送到你的出生点"
          - "警告：任何移动都会中断传送过程"
    - price:
      - type: GOLD_INGOT
        amount: 10
      reward:
      - type: SHEEP_SPAWN_EGG
        meta:
          ==: ItemMeta
          meta-type: UNSPECIFIC
          display-name: "TNT羊"
          lore:
          - "使用TNT羊！它会"
          - "走向你最近的敌人"
          - "并在8秒内爆炸！"
          internal: H4sIAAAAAAAAAONiYOBi4HTNK8ksqQxJTOdgYMpMYWANzkhNLWBgAAABt1TGHQAAAA==
    - price:
      - type: IRON_INGOT
        amount: 5
      reward:
      - type: COMPASS
  drinks:
    item: POTION
    name: "药水"
    order: 80
    permission: bw.base
    lore:
    - "还不够强？使用这些药水，你会感觉更好！"
    offers:
    - price:
      - type: IRON_INGOT
        amount: 3
      reward:
      - type: POTION
        meta:
          ==: ItemMeta
          meta-type: POTION
          potion-type: healing
    - price:
      - type: IRON_INGOT
        amount: 5
      reward:
      - type: POTION
        meta:
          ==: ItemMeta
          meta-type: POTION
          potion-type: strong_healing
    - price:
      - type: IRON_INGOT
        amount: 7
      reward:
      - type: POTION
        meta:
          ==: ItemMeta
          meta-type: POTION
          potion-type: swiftness
    - price:
      - type: GOLD_INGOT
      reward:
      - type: POTION
        meta:
          ==: ItemMeta
          meta-type: POTION
          potion-type: strength
    - price:
      - type: GOLD_INGOT
        amount: 3
      reward:
      - type: POTION
        meta:
          ==: ItemMeta
          meta-type: POTION
          potion-type: regeneration
schema-version: 1