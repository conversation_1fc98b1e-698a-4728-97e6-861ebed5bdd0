version: 2
executorType: docker
containerInfo:
  - image: maven:3.5-jdk-8
jobs:
  build:
    branches:
      ignore:
        - l10n
    working_directory: "~/BedwarsRel"
    steps:
      - type: checkout
      - run:
          name: "Getting Dependencies"
          command: './get-dependencies.sh'
          working_directory: "~/BedwarsRel/build"
      - run:
          name: "Building and deploying BedwarsRel"
          command: |
            export SHORT_HASH=$(echo $CIRCLE_SHA1 | cut -c -7)
            if [ "${CIRCLE_BRANCH}" == "master" ]
            then
              mvn deploy -fae -P ci-release,deploy-snapshot,attach-javadocs,attach-sources --settings settings.xml
            elif [ "${CIRCLE_BRANCH}" == "develop" ]
            then
              mvn deploy -fae -P ci-snapshot,deploy-snapshot,attach-javadocs,attach-sources --settings settings.xml
            else
              mvn clean install -fae -P ci-snapshot,attach-javadocs,attach-sources
            fi
            mkdir -p plugin/target/artifacts/
            cp plugin/target/BedwarsRel-*.jar plugin/target/artifacts/.
      - type: artifacts-store
        path: plugin/target/artifacts
        destination: artifacts

      - type: artifacts-store
        path: plugin/target/apidocs
        destination: apidocs