@echo off
echo ========================================
echo BedwarsRel 1.20 一键构建脚本
echo ========================================
echo.

echo [1/5] 清理旧的构建文件...
if exist "build\BedwarsRel-1.20-*.jar" del "build\BedwarsRel-1.20-*.jar"
if exist "temp" rmdir /s /q "temp"

echo [2/5] 构建 Common 模块...
cd common
call mvn clean package -q -DskipTests
if %errorlevel% neq 0 (
    echo 错误: Common模块构建失败!
    pause
    exit /b 1
)
cd ..

echo [3/5] 构建 v1_20_r1 模块...
cd v1_20_r1
call mvn clean package -q -DskipTests
if %errorlevel% neq 0 (
    echo 错误: v1_20_r1模块构建失败!
    pause
    exit /b 1
)
cd ..

echo [4/5] 合并JAR文件...
mkdir temp
cd temp
jar -xf "..\common\target\BedwarsRel-Common-1.3.6.jar"
jar -xf "..\v1_20_r1\target\BedwarsRel-v1_20_r1-1.3.6.jar"

set timestamp=%date:~0,4%%date:~5,2%%date:~8,2%-%time:~0,2%%time:~3,2%
set timestamp=%timestamp: =0%
jar -cf "..\build\BedwarsRel-1.20-1.3.6-%timestamp%.jar" .
cd ..
rmdir /s /q temp

echo [5/5] 清理临时文件...
echo.

echo ========================================
echo 构建完成!
echo ========================================
echo 生成的文件: build\BedwarsRel-1.20-1.3.6-%timestamp%.jar
echo.
echo 部署说明:
echo 1. 将JAR文件复制到服务器的 plugins 目录
echo 2. 重启服务器
echo 3. 使用 /bw 命令测试功能
echo.
echo 功能特色:
echo - 完整的床战游戏系统
echo - 智能TNT羊功能
echo - 全息图支持 (需HolographicDisplays)
echo - 1.20完全兼容
echo.
pause
