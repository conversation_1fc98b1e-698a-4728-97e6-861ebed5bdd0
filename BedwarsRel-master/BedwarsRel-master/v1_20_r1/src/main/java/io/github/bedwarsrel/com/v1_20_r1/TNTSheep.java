package io.github.bedwarsrel.com.v1_20_r1;

import io.github.bedwarsrel.BedwarsRel;
import io.github.bedwarsrel.shop.Specials.ITNTSheep;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Creature;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.entity.Sheep;
import org.bukkit.entity.TNTPrimed;

/**
 * 1.20版本的TNT羊实现
 * 使用Bukkit API而不是NMS，简化但功能完整
 */
public class TNTSheep implements ITNTSheep {

  private Sheep sheep = null;
  private TNTPrimed primedTnt = null;
  private World world = null;
  private Player target = null;

  public TNTSheep(Location location, Player target) {
    this.world = location.getWorld();
    this.target = target;
    
    // 使用Bukkit API创建羊
    this.sheep = (Sheep) world.spawnEntity(location, org.bukkit.entity.EntityType.SHEEP);
    
    // 设置羊的属性
    this.sheep.setCustomName("§cTNT羊");
    this.sheep.setCustomNameVisible(false);
    this.sheep.setRemoveWhenFarAway(false);
    
    // 设置移动速度
    try {
      this.sheep.getAttribute(org.bukkit.attribute.Attribute.GENERIC_MOVEMENT_SPEED)
          .setBaseValue(BedwarsRel.getInstance().getConfig().getDouble("specials.tntsheep.speed", 0.4D));
      this.sheep.getAttribute(org.bukkit.attribute.Attribute.GENERIC_FOLLOW_RANGE)
          .setBaseValue(128D);
    } catch (Exception e) {
      // 如果属性设置失败，使用默认值
    }
    
    // 设置目标
    if (this.sheep instanceof Creature) {
      ((Creature) this.sheep).setTarget(target);
    }
  }

  @Override
  public Location getLocation() {
    if (this.sheep != null) {
      return this.sheep.getLocation();
    }
    return null;
  }

  @Override
  public TNTPrimed getTNT() {
    return this.primedTnt;
  }

  @Override
  public void setTNT(TNTPrimed tnt) {
    this.primedTnt = tnt;
  }

  @Override
  public void remove() {
    if (this.sheep != null) {
      this.sheep.remove();
    }
    if (this.primedTnt != null) {
      this.primedTnt.remove();
    }
  }

  @Override
  public void setPassenger(TNTPrimed tnt) {
    if (this.sheep != null && tnt != null) {
      // 1.20版本使用addPassenger方法
      this.sheep.addPassenger(tnt);
    }
  }

  @Override
  public void setTNTSource(Entity source) {
    if (this.primedTnt != null && source != null) {
      try {
        // 1.20版本设置TNT来源
        this.primedTnt.setSource(source);
      } catch (Exception e) {
        // 如果设置失败，记录但不中断
        BedwarsRel.getInstance().getLogger().warning("无法设置TNT来源: " + e.getMessage());
      }
    }
  }

  /**
   * 获取Bukkit羊实体
   */
  public Sheep getSheep() {
    return this.sheep;
  }

  /**
   * 获取目标玩家
   */
  public Player getTarget() {
    return this.target;
  }

  /**
   * 设置新的目标
   */
  public void setTarget(Player newTarget) {
    this.target = newTarget;
    if (this.sheep instanceof Creature) {
      ((Creature) this.sheep).setTarget(newTarget);
    }
  }

  /**
   * 检查羊是否还活着
   */
  public boolean isAlive() {
    return this.sheep != null && this.sheep.isValid() && !this.sheep.isDead();
  }

  /**
   * 引爆TNT
   */
  public void explode() {
    if (this.primedTnt != null) {
      this.primedTnt.setFuseTicks(0);
    }
  }
}
