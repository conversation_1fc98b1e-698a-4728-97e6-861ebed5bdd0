package io.github.bedwarsrel.com.v1_20_r1;

import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

public class ActionBar {

  public static void sendActionBar(Player player, String message) {
    // 1.20版本使用Bukkit API发送ActionBar
    String s = ChatColor.translateAlternateColorCodes('&', message.replace("_", " "));
    try {
      // 使用Spigot API发送ActionBar
      player.spigot().sendMessage(net.md_5.bungee.api.ChatMessageType.ACTION_BAR,
          net.md_5.bungee.api.chat.TextComponent.fromLegacyText(s));
    } catch (Exception e) {
      // 如果Spigot API不可用，发送到聊天栏作为备选
      player.sendMessage(s);
    }
  }

}
