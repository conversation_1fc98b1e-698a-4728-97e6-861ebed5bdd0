package io.github.bedwarsrel.com.v1_20_r1;

import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

public class PerformRespawnRunnable extends BukkitRunnable {

  private Player player = null;

  public PerformRespawnRunnable(Player player) {
    this.player = player;
  }

  @Override
  public void run() {
    // 1.20版本使用Spigot API进行重生
    try {
      player.spigot().respawn();
    } catch (Exception e) {
      // 如果Spigot API不可用，使用传统方法
      // 这里可以添加其他重生逻辑
    }
  }

}
