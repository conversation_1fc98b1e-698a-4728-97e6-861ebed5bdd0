/*******************************************************************************
 * This file is part of ASkyBlock.
 *
 * ASkyBlock is free software: you can redistribute it and/or modify it under the terms of the GNU
 * General Public License as published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * ASkyBlock is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without
 * even the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License along with ASkyBlock. If not,
 * see <http://www.gnu.org/licenses/>.
 *******************************************************************************/
package io.github.bedwarsrel.com.v1_20_r1;

import org.bukkit.Material;
import org.bukkit.entity.EntityType;
import org.bukkit.inventory.ItemStack;

public class SpawnEgg1_20 {

  private EntityType type;

  public SpawnEgg1_20(EntityType type) {
    this.type = type;
  }

  /**
   * Converts from an item stack to a spawn egg 1.20
   *
   * @param item - ItemStack, quantity is disregarded
   * @return SpawnEgg 1.20
   */
  public static SpawnEgg1_20 fromItemStack(ItemStack item) {
    if (item == null) {
      throw new IllegalArgumentException("item cannot be null");
    }
    
    // 在1.20中，每个实体都有自己的生成蛋材料
    Material material = item.getType();
    EntityType entityType = getEntityTypeFromMaterial(material);
    
    if (entityType != null) {
      return new SpawnEgg1_20(entityType);
    }
    
    // 1.20版本简化处理，直接使用默认实体类型
    if (material.name().contains("SPAWN_EGG")) {
      return new SpawnEgg1_20(EntityType.SHEEP);
    }
    
    return null;
  }

  private static EntityType getEntityTypeFromMaterial(Material material) {
    switch (material) {
      case SHEEP_SPAWN_EGG:
        return EntityType.SHEEP;
      case COW_SPAWN_EGG:
        return EntityType.COW;
      case PIG_SPAWN_EGG:
        return EntityType.PIG;
      case CHICKEN_SPAWN_EGG:
        return EntityType.CHICKEN;
      case VILLAGER_SPAWN_EGG:
        return EntityType.VILLAGER;
      // 添加更多实体类型映射
      default:
        return null;
    }
  }

  public SpawnEgg1_20 clone() {
    return new SpawnEgg1_20(this.type);
  }

  /**
   * Get the type of entity this egg will spawn.
   *
   * @return The entity type.
   */
  public EntityType getSpawnedType() {
    return type;
  }

  /**
   * Set the type of entity this egg will spawn.
   *
   * @param type The entity type.
   */
  public void setSpawnedType(EntityType type) {
    if (type.isAlive()) {
      this.type = type;
    }
  }

  /**
   * Get an ItemStack of one spawn egg
   *
   * @return ItemStack
   */
  public ItemStack toItemStack() {
    return toItemStack(1);
  }

  /**
   * Get an itemstack of spawn eggs
   *
   * @return ItemStack of spawn eggs
   */
  public ItemStack toItemStack(int amount) {
    Material spawnEggMaterial = getSpawnEggMaterial(type);
    if (spawnEggMaterial != null) {
      return new ItemStack(spawnEggMaterial, amount);
    }
    
    // 1.20版本简化处理，直接返回对应的生成蛋
    return new ItemStack(Material.SHEEP_SPAWN_EGG, amount);
  }

  private Material getSpawnEggMaterial(EntityType entityType) {
    switch (entityType) {
      case SHEEP:
        return Material.SHEEP_SPAWN_EGG;
      case COW:
        return Material.COW_SPAWN_EGG;
      case PIG:
        return Material.PIG_SPAWN_EGG;
      case CHICKEN:
        return Material.CHICKEN_SPAWN_EGG;
      case VILLAGER:
        return Material.VILLAGER_SPAWN_EGG;
      // 添加更多实体类型映射
      default:
        return null;
    }
  }

  public String toString() {
    return "SPAWN EGG{" + getSpawnedType() + "}";
  }
}
