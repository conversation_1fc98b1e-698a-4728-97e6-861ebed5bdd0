package io.github.bedwarsrel.com.v1_20_r1;

import io.github.bedwarsrel.BedwarsRel;
import io.github.bedwarsrel.game.Game;
import io.github.bedwarsrel.utils.Utils;
import io.github.bedwarsrel.villager.MerchantCategory;
import io.github.bedwarsrel.villager.VillagerTrade;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.entity.Villager;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.MerchantRecipe;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;

public class VillagerItemShop {

  private MerchantCategory category = null;
  private Game game = null;
  private Player player = null;

  public VillagerItemShop(Game g, Player p, MerchantCategory category) {
    this.game = g;
    this.player = p;
    this.category = category;
  }

  private Villager createVillager() {
    try {
      // 1.20版本使用Bukkit API创建村民
      Villager villager = (Villager) this.game.getRegion().getWorld().spawnEntity(
          this.player.getLocation(), org.bukkit.entity.EntityType.VILLAGER);
      return villager;
    } catch (Exception e) {
      // 简化错误处理，避免Bugsnag依赖问题
      e.printStackTrace();
    }

    return null;
  }

  public void openTrading() {
    // As task because of inventory issues
    new BukkitRunnable() {

      @SuppressWarnings("deprecation")
      @Override
      public void run() {
        try {
          Villager villager = VillagerItemShop.this.createVillager();
          if (villager == null) {
            return;
          }

          // set location
          List<MerchantRecipe> recipeList = new ArrayList<MerchantRecipe>();

          for (VillagerTrade trade : VillagerItemShop.this.category
              .getFilteredOffers()) {
            ItemStack reward = trade.getRewardItem();
            Method colorable = Utils.getColorableMethod(reward.getType());

            if (Utils.isColorable(reward)) {
              // 在1.20中，羊毛数据值系统已经改变
              // 我们需要使用新的颜色系统
              if (reward.getType().name().contains("WOOL")) {
                // 获取对应颜色的羊毛材料
                String colorName = game.getPlayerTeam(player).getColor().getDyeColor().name();
                Material coloredWool = Material.getMaterial(colorName + "_WOOL");
                if (coloredWool != null) {
                  reward.setType(coloredWool);
                }
              }
            } else if (colorable != null) {
              ItemMeta meta = reward.getItemMeta();
              colorable.setAccessible(true);
              colorable.invoke(meta, new Object[]{VillagerItemShop.this.game
                  .getPlayerTeam(VillagerItemShop.this.player).getColor().getColor()});
              reward.setItemMeta(meta);
            }

            MerchantRecipe recipe = new MerchantRecipe(trade.getRewardItem(), 1024);
            recipe.addIngredient(trade.getItem1());

            if (trade.getItem2() != null) {
              recipe.addIngredient(trade.getItem2());
            } else {
              recipe.addIngredient(new ItemStack(Material.AIR));
            }

            recipe.setUses(0);
            recipe.setExperienceReward(false);
            recipeList.add(recipe);
          }

          // 1.20版本使用Bukkit API设置村民交易
          villager.setRecipes(recipeList);

          // 直接打开村民交易界面
          VillagerItemShop.this.player.openMerchant(villager, true);
        } catch (Exception ex) {
          // 简化错误处理，避免Bugsnag依赖问题
          ex.printStackTrace();
        }
      }
    }.runTask(BedwarsRel.getInstance());
  }

}
