package io.github.bedwarsrel.com.v1_20_r1;

import java.util.List;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.entity.Player;

public class ParticleSpawner {

  public static void spawnParticle(List<Player> players, String particle, float x, float y,
      float z) {
    // 1.20版本使用Bukkit API生成粒子
    Particle bukkitParticle = Particle.FLAME;

    // 根据粒子名称匹配对应的Bukkit粒子类型
    switch (particle.toLowerCase()) {
      case "fireworks_spark":
      case "firework":
        bukkitParticle = Particle.FLAME; // 使用火焰粒子代替
        break;
      case "flame":
        bukkitParticle = Particle.FLAME;
        break;
      case "smoke":
        bukkitParticle = Particle.CAMPFIRE_COSY_SMOKE; // 1.20中的烟雾粒子
        break;
      default:
        bukkitParticle = Particle.FLAME;
        break;
    }

    for (Player player : players) {
      try {
        Location location = new Location(player.getWorld(), x, y, z);
        player.spawnParticle(bukkitParticle, location, 1);
      } catch (Exception e) {
        // 忽略粒子生成错误
      }
    }
  }

}
