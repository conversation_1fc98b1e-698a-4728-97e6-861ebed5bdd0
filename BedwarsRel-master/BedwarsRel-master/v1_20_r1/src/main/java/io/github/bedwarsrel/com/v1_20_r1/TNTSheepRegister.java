package io.github.bedwarsrel.com.v1_20_r1;

import io.github.bedwarsrel.BedwarsRel;
import io.github.bedwarsrel.shop.Specials.ITNTSheep;
import io.github.bedwarsrel.shop.Specials.ITNTSheepRegister;
import org.bukkit.DyeColor;
import org.bukkit.Location;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.entity.Sheep;
import org.bukkit.entity.TNTPrimed;
import org.bukkit.scheduler.BukkitRunnable;

/**
 * 1.20版本的TNT羊注册器
 * 使用Bukkit API实现，不依赖NMS
 */
public class TNTSheepRegister implements ITNTSheepRegister {

  @Override
  public void registerEntities(int entityId) {
    // 1.20版本不需要注册自定义实体，使用原生羊实体
    // 这个方法保留为兼容性，但不执行任何操作
    BedwarsRel.getInstance().getLogger().info("TNT羊实体注册完成 (使用原生羊实体)");
  }

  @Override
  public ITNTSheep spawnCreature(
      final io.github.bedwarsrel.shop.Specials.TNTSheep specialItem,
      final Location location, final Player owner, Player target, final DyeColor color) {
    
    // 创建TNT羊
    final TNTSheep tntSheep = new TNTSheep(location, target);
    
    // 设置羊的颜色
    if (tntSheep.getSheep() != null) {
      tntSheep.getSheep().setColor(color);
    }
    
    // 延迟添加TNT，确保羊已经完全生成
    new BukkitRunnable() {
      @Override
      public void run() {
        try {
          // 在羊的位置上方生成TNT
          Location tntLocation = tntSheep.getLocation().clone().add(0.0, 1.0, 0.0);
          TNTPrimed primedTnt = (TNTPrimed) location.getWorld()
              .spawnEntity(tntLocation, EntityType.PRIMED_TNT);
          
          // 设置TNT属性
          primedTnt.setYield((float) (primedTnt.getYield()
              * BedwarsRel.getInstance().getConfig().getDouble("specials.tntsheep.explosion-factor", 1.0)));
          primedTnt.setFuseTicks((int) Math.round(
              BedwarsRel.getInstance().getConfig().getDouble("specials.tntsheep.fuse-time", 8) * 20));
          primedTnt.setIsIncendiary(false);
          
          // 设置TNT来源
          tntSheep.setTNTSource(owner);
          
          // 让TNT骑在羊上
          tntSheep.setPassenger(primedTnt);
          tntSheep.setTNT(primedTnt);
          
          // 添加到游戏区域的移除列表
          specialItem.getGame().getRegion().addRemovingEntity(primedTnt);
          specialItem.getGame().getRegion().addRemovingEntity(tntSheep.getSheep());
          
          // 更新特殊物品状态
          specialItem.updateTNT();
          
          // 启动TNT羊的AI任务
          startTNTSheepAI(tntSheep, specialItem);
          
        } catch (Exception e) {
          BedwarsRel.getInstance().getLogger().warning("创建TNT羊时出错: " + e.getMessage());
          e.printStackTrace();
        }
      }
    }.runTaskLater(BedwarsRel.getInstance(), 5L);

    return tntSheep;
  }
  
  /**
   * 启动TNT羊的AI任务
   */
  private void startTNTSheepAI(final TNTSheep tntSheep, final io.github.bedwarsrel.shop.Specials.TNTSheep specialItem) {
    new BukkitRunnable() {
      private int tickCount = 0;
      private final int maxTicks = 20 * 30; // 30秒后自动移除
      
      @Override
      public void run() {
        tickCount++;
        
        // 检查羊是否还存在
        if (!tntSheep.isAlive()) {
          this.cancel();
          return;
        }
        
        // 检查是否超时
        if (tickCount >= maxTicks) {
          tntSheep.explode();
          this.cancel();
          return;
        }
        
        // 检查目标是否还在范围内
        Player target = tntSheep.getTarget();
        if (target != null && target.isOnline()) {
          Location sheepLoc = tntSheep.getLocation();
          Location targetLoc = target.getLocation();
          
          if (sheepLoc != null && targetLoc != null) {
            double distance = sheepLoc.distance(targetLoc);
            
            // 如果距离很近，引爆TNT
            if (distance < 2.0) {
              tntSheep.explode();
              this.cancel();
              return;
            }
            
            // 如果距离太远，重新设置目标
            if (distance > 50.0) {
              // 寻找附近的其他玩家作为目标
              Player nearestPlayer = findNearestEnemy(sheepLoc, specialItem);
              if (nearestPlayer != null) {
                tntSheep.setTarget(nearestPlayer);
              }
            }
          }
        } else {
          // 目标不存在，寻找新目标
          Player nearestPlayer = findNearestEnemy(tntSheep.getLocation(), specialItem);
          if (nearestPlayer != null) {
            tntSheep.setTarget(nearestPlayer);
          } else {
            // 没有目标，5秒后自爆
            if (tickCount % 100 == 0) { // 每5秒检查一次
              tntSheep.explode();
              this.cancel();
              return;
            }
          }
        }
      }
    }.runTaskTimer(BedwarsRel.getInstance(), 20L, 5L); // 每5tick运行一次
  }
  
  /**
   * 寻找最近的敌人
   */
  private Player findNearestEnemy(Location location, io.github.bedwarsrel.shop.Specials.TNTSheep specialItem) {
    if (location == null) return null;
    
    Player nearestPlayer = null;
    double nearestDistance = Double.MAX_VALUE;
    
    for (Player player : location.getWorld().getPlayers()) {
      if (player.isOnline() && !player.equals(specialItem.getPlayer())) {
        // 检查是否是敌人（不在同一队伍）
        if (specialItem.getGame().getPlayerTeam(specialItem.getPlayer()) !=
            specialItem.getGame().getPlayerTeam(player)) {
          
          double distance = location.distance(player.getLocation());
          if (distance < nearestDistance && distance < 30.0) { // 30格范围内
            nearestDistance = distance;
            nearestPlayer = player;
          }
        }
      }
    }
    
    return nearestPlayer;
  }
}
