<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>io.github.bedwarsrel</groupId>
  <artifactId>BedwarsRel-Parent</artifactId>
  <packaging>pom</packaging>
  <version>1.3.6</version>

  <name>BedwarsRel</name>
  <description>A Bedwars Plugin for Minecraft servers.</description>
  <url>https://bedwarsrel.github.io</url>
  <licenses>
    <license>
      <name>GNU GENERAL PUBLIC LICENSE, Version 3.0</name>
      <url>https://github.com/BedwarsRel/BedwarsRel/blob/master/LICENSE</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>BedwarsRel-Team</name>
      <organizationUrl>https://github.com/orgs/BedwarsRel/people</organizationUrl>
    </developer>
  </developers>
  <scm>
    <url>https://github.com/BedwarsRel/BedwarsRel/tree/master/</url>
    <connection>scm:git:ssh://github.com:BedwarsRel/BedwarsRel.git</connection>
    <developerConnection>scm:git:ssh://github.com:BedwarsRel/BedwarsRel.git</developerConnection>
  </scm>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/BedwarsRel/BedwarsRel/issues</url>
  </issueManagement>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <modules>
    <module>v1_8_r1</module>
    <module>v1_8_r2</module>
    <module>v1_8_r3</module>
    <module>v1_9_r1</module>
    <module>v1_9_r2</module>
    <module>v1_10_r1</module>
    <module>v1_11_r1</module>
    <module>v1_12_r1</module>
    <module>v1_20_r1</module>
    <module>common</module>
    <module>plugin</module>
  </modules>

  <profiles>
    <profile>
      <id>default</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <versionstring>${project.parent.version}-SNAPSHOT</versionstring>
        <filename>${project.artifactId}-${project.parent.version}-SNAPSHOT</filename>
      </properties>
    </profile>
    <profile>
      <id>ci-snapshot</id>
      <properties>
        <versionstring>${project.parent.version}-${env.SHORT_HASH}-SNAPSHOT</versionstring>
        <filename>${project.artifactId}-${project.parent.version}-SNAPSHOT</filename>
      </properties>
    </profile>
    <profile>
      <id>ci-release</id>
      <properties>
        <versionstring>${project.parent.version}-SNAPSHOT</versionstring>
        <filename>${project.artifactId}-${project.parent.version}</filename>
      </properties>
    </profile>
    <profile>
      <id>release</id>
      <properties>
        <versionstring>${project.parent.version}</versionstring>
        <filename>${project.artifactId}-${project.parent.version}</filename>
      </properties>
    </profile>
    <profile>
      <id>deploy-snapshot</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.sonatype.plugins</groupId>
            <artifactId>nexus-staging-maven-plugin</artifactId>
            <version>1.6.7</version>
            <extensions>true</extensions>
            <configuration>
              <serverId>sonatype-nexus-snapshots</serverId>
              <nexusUrl>https://oss.sonatype.org/</nexusUrl>
              <autoReleaseAfterClose>true</autoReleaseAfterClose>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>deploy-release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.sonatype.plugins</groupId>
            <artifactId>nexus-staging-maven-plugin</artifactId>
            <version>1.6.7</version>
            <extensions>true</extensions>
            <configuration>
              <serverId>sonatype-nexus-releases</serverId>
              <nexusUrl>https://oss.sonatype.org/</nexusUrl>
              <autoReleaseAfterClose>true</autoReleaseAfterClose>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>attach-javadocs</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>2.9.1</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <includeDependencySources>true</includeDependencySources>
                  <dependencySourceIncludes>
                    <dependencySourceInclude>io.github.bedwarsrel:*</dependencySourceInclude>
                  </dependencySourceIncludes>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>attach-sources</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>2.2.1</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>sign-build</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.5</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <repositories>
    <repository>
      <id>spigot-repo</id>
      <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
    </repository>
    <repository>
      <id>inventive-repo</id>
      <url>https://repo.inventivetalent.org/content/repositories/releases</url>
    </repository>
  </repositories>

  <dependencies>
    <dependency>
      <groupId>org.spigotmc</groupId>
      <artifactId>spigot-api</artifactId>
      <version>1.20.1-R0.1-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.30</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <configuration>
          <source>17</source>
          <target>17</target>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>