<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>BedwarsRel-Parent</artifactId>
    <groupId>io.github.bedwarsrel</groupId>
    <version>1.3.6</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <version>${versionstring}</version>

  <artifactId>BedwarsRel</artifactId>

  <dependencies>
    <dependency>
      <groupId>io.github.bedwarsrel</groupId>
      <artifactId>BedwarsRel-Common</artifactId>
      <version>${project.parent.version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.github.bedwarsrel.com</groupId>
      <artifactId>BedwarsRel-v1_20_r1</artifactId>
      <version>${project.parent.version}</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>../common/src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.6</version>
        <configuration>
          <finalName>${filename}</finalName>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>2.4.3</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <artifactSet>
                <excludes>
                  <exclude>com.google.guava:guava</exclude>
                </excludes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>com.bugsnag</pattern>
                  <shadedPattern>thirdparty.com.bugsnag</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.fasterxml.jackson</pattern>
                  <shadedPattern>thirdparty.com.fasterxml.jackson</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.apache.log4j</pattern>
                  <shadedPattern>thirdparty.org.apache.log4j</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.slf4j</pattern>
                  <shadedPattern>thirdparty.org.slf4j</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.zaxxer</pattern>
                  <shadedPattern>thirdparty.com.zaxxer.HikariCP-java7</shadedPattern>
                </relocation>
              </relocations>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <distributionManagement>
    <repository>
      <id>sonatype-nexus-releases</id>
      <url>https://oss.sonatype.org/content/repositories/releases</url>
    </repository>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>

</project>