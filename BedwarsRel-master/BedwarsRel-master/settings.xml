<settings xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/SETTINGS/1.0.0"
  xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <servers>
    <server>
      <id>sonatype-nexus-releases</id>
      <username>${env.ossrhuser}</username>
      <password>${env.ossrhpassword}</password>
    </server>
    <server>
      <id>sonatype-nexus-snapshots</id>
      <username>${env.ossrhuser}</username>
      <password>${env.ossrhpassword}</password>
    </server>
  </servers>
</settings>