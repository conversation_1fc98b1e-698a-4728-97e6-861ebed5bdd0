#!/usr/bin/env python3
"""
批量修复BedwarsRel项目中的1.20材料兼容性问题
"""

import os
import re

# 材料映射表
MATERIAL_MAPPINGS = {
    'Material.BED_BLOCK': 'MaterialCompatibility.getCompatibleMaterial("BED_BLOCK")',
    'Material.BED': 'MaterialCompatibility.getCompatibleMaterial("BED")',
    'Material.SKULL_ITEM': 'MaterialCompatibility.getCompatibleMaterial("SKULL_ITEM")',
    'Material.SNOW_BALL': 'MaterialCompatibility.getCompatibleMaterial("SNOW_BALL")',
    'Material.MONSTER_EGG': 'MaterialCompatibility.getCompatibleMaterial("MONSTER_EGG")',
    'Material.MONSTER_EGGS': 'MaterialCompatibility.getCompatibleMaterial("MONSTER_EGGS")',
    'Material.STAINED_GLASS': 'MaterialCompatibility.getCompatibleMaterial("STAINED_GLASS")',
    'Material.WOOL': 'MaterialCompatibility.getCompatibleMaterial("WOOL")',
    'Material.STAINED_CLAY': 'MaterialCompatibility.getCompatibleMaterial("STAINED_CLAY")',
    'Material.SULPHUR': 'MaterialCompatibility.getCompatibleMaterial("SULPHUR")',
    'Material.EYE_OF_ENDER': 'MaterialCompatibility.getCompatibleMaterial("EYE_OF_ENDER")',
    'Material.STATIONARY_WATER': 'MaterialCompatibility.getCompatibleMaterial("STATIONARY_WATER")',
    'Material.STATIONARY_LAVA': 'MaterialCompatibility.getCompatibleMaterial("STATIONARY_LAVA")',
    'Material.WEB': 'MaterialCompatibility.getCompatibleMaterial("WEB")',
    'Material.SOIL': 'MaterialCompatibility.getCompatibleMaterial("SOIL")',
}

def add_import_if_needed(file_path, content):
    """添加MaterialCompatibility导入如果需要的话"""
    if 'MaterialCompatibility.getCompatibleMaterial' in content:
        if 'import io.github.bedwarsrel.utils.MaterialCompatibility;' not in content:
            # 在package声明后添加import
            package_match = re.search(r'(package [^;]+;)', content)
            if package_match:
                package_line = package_match.group(1)
                import_line = '\n\nimport io.github.bedwarsrel.utils.MaterialCompatibility;'
                content = content.replace(package_line, package_line + import_line)
    return content

def fix_file(file_path):
    """修复单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换材料引用
        for old_material, new_material in MATERIAL_MAPPINGS.items():
            content = content.replace(old_material, new_material)
        
        # 添加导入
        content = add_import_if_needed(file_path, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed: {file_path}")
            return True
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False
    
    return False

def main():
    """主函数"""
    base_dir = "common/src/main/java"
    fixed_count = 0
    
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                if fix_file(file_path):
                    fixed_count += 1
    
    print(f"Total files fixed: {fixed_count}")

if __name__ == "__main__":
    main()
