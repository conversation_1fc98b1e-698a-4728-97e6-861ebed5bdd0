<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  
  <modelVersion>4.0.0</modelVersion>
  
  <groupId>io.github.bedwarsrel</groupId>
  <artifactId>BedwarsRel-1.20-Chinese</artifactId>
  <version>1.3.6</version>
  <packaging>jar</packaging>
  
  <name>BedwarsRel 1.20 汉化版</name>
  <description>BedwarsRel 1.20 完全汉化版本，合并了Common和v1_20_r1模块</description>
  
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
  </properties>
  
  <repositories>
    <repository>
      <id>spigot-repo</id>
      <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
    </repository>
    <repository>
      <id>inventive-repo</id>
      <url>https://repo.inventivetalent.org/content/repositories/releases</url>
    </repository>
  </repositories>
  
  <dependencies>
    <!-- Spigot API -->
    <dependency>
      <groupId>org.spigotmc</groupId>
      <artifactId>spigot-api</artifactId>
      <version>1.20.1-R0.1-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    
    <!-- Paper API for 1.20 support -->
    <dependency>
      <groupId>io.papermc.paper</groupId>
      <artifactId>paper-api</artifactId>
      <version>1.20.1-R0.1-SNAPSHOT</version>
      <scope>system</scope>
      <systemPath>${pom.basedir}/../../../lib/paper-1.20-17.jar</systemPath>
    </dependency>
    
    <!-- Lombok -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.30</version>
      <scope>provided</scope>
    </dependency>
    
    <!-- HolographicDisplays API -->
    <dependency>
      <groupId>com.gmail.filoghost.holographicdisplays</groupId>
      <artifactId>api</artifactId>
      <version>1.0</version>
      <scope>system</scope>
      <systemPath>${pom.basedir}/../../../lib/HolographicDisplaysAPI_v2.1.7.jar</systemPath>
    </dependency>

    <dependency>
      <groupId>com.gmail.filoghost.holographicdisplays</groupId>
      <artifactId>holographicdisplays-api</artifactId>
      <version>2.1.7</version>
      <scope>system</scope>
      <systemPath>${pom.basedir}/../../../lib/HolographicDisplaysAPI_v2.1.7.jar</systemPath>
    </dependency>
    
    <!-- Bugsnag for error reporting -->
    <dependency>
      <groupId>com.bugsnag</groupId>
      <artifactId>bugsnag</artifactId>
      <version>3.0.2</version>
      <scope>compile</scope>
    </dependency>
    
    <!-- SLF4J Logging -->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-log4j12</artifactId>
      <version>1.7.22</version>
      <scope>compile</scope>
    </dependency>
    
    <!-- HikariCP for database connection pooling -->
    <dependency>
      <groupId>com.zaxxer</groupId>
      <artifactId>HikariCP-java7</artifactId>
      <version>2.4.11</version>
      <scope>compile</scope>
    </dependency>
    
    <!-- JSON Simple -->
    <dependency>
      <groupId>com.googlecode.json-simple</groupId>
      <artifactId>json-simple</artifactId>
      <version>1.1.1</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  
  <build>
    <!-- 指定源代码目录 -->
    <sourceDirectory>src/main/java</sourceDirectory>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    
    <plugins>
      <!-- Maven Compiler Plugin -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <configuration>
          <source>17</source>
          <target>17</target>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      
      <!-- Maven Resources Plugin -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.3.1</version>
        <configuration>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      
      <!-- Maven Shade Plugin for creating fat JAR -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.5.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <createDependencyReducedPom>false</createDependencyReducedPom>
              <shadedArtifactAttached>false</shadedArtifactAttached>
              <finalName>BedwarsRel-1.20-1.3.6-汉化版</finalName>
              
              <!-- 包含依赖 -->
              <artifactSet>
                <includes>
                  <include>com.bugsnag:bugsnag</include>
                  <include>org.slf4j:slf4j-log4j12</include>
                  <include>com.zaxxer:HikariCP-java7</include>
                  <include>com.googlecode.json-simple:json-simple</include>
                </includes>
              </artifactSet>
              
              <!-- 重定位包以避免冲突 -->
              <relocations>
                <relocation>
                  <pattern>com.bugsnag</pattern>
                  <shadedPattern>io.github.bedwarsrel.libs.bugsnag</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.slf4j</pattern>
                  <shadedPattern>io.github.bedwarsrel.libs.slf4j</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.zaxxer</pattern>
                  <shadedPattern>io.github.bedwarsrel.libs.zaxxer</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.json.simple</pattern>
                  <shadedPattern>io.github.bedwarsrel.libs.json</shadedPattern>
                </relocation>
              </relocations>
              
              <!-- 过滤器 -->
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  
</project>
