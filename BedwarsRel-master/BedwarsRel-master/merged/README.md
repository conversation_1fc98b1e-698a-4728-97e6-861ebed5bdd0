# BedwarsRel 1.20 汉化版

这是一个完全汉化的BedwarsRel插件版本，专为Minecraft 1.20服务器设计。

## 特色功能

### 🇨🇳 完整汉化
- **配置文件汉化**: config.yml和shop.yml完全中文化
- **注释汉化**: 所有配置项注释翻译为中文，便于理解和配置
- **商店界面汉化**: 游戏内商店完全中文化
- **默认中文**: 插件启动时自动使用中文界面

### 🎮 游戏功能
- **完整的床战游戏系统**: 支持多人床战游戏
- **智能TNT羊功能**: 自动寻敌爆炸功能
- **全息图支持**: 需要HolographicDisplays插件
- **1.20完全兼容**: 支持最新的Minecraft 1.20版本

### 🛠️ 技术特性
- **合并构建**: Common和v1_20_r1模块合并为单个JAR文件
- **依赖内置**: 包含所有必要的依赖库
- **优化性能**: 针对1.20版本优化

## 构建说明

### 前置要求
- Java 17 或更高版本
- Maven 3.6 或更高版本
- HolographicDisplaysAPI_v2.1.7.jar (放在 ../lib/ 目录中)

### 构建步骤

#### 方法一：使用构建脚本（推荐）
```bash
# Windows
build-chinese.bat

# Linux/Mac
chmod +x build-chinese.sh
./build-chinese.sh
```

#### 方法二：手动构建
```bash
mvn clean package -DskipTests
```

### 构建输出
构建成功后，在 `target/` 目录中会生成：
- `BedwarsRel-1.20-1.3.6-汉化版.jar` - 完整的插件文件

## 安装说明

1. **下载依赖**:
   - 确保服务器运行 Spigot/Paper 1.20+
   - 可选：安装 HolographicDisplays 插件以支持全息图功能

2. **安装插件**:
   - 将 `BedwarsRel-1.20-1.3.6-汉化版.jar` 复制到服务器的 `plugins/` 目录
   - 重启服务器

3. **配置插件**:
   - 插件会自动生成中文配置文件
   - 根据需要修改 `plugins/BedwarsRel/config.yml`

## 使用说明

### 基本命令
- `/bw` - 显示插件帮助
- `/bw join <游戏名>` - 加入游戏
- `/bw leave` - 离开游戏
- `/bw stats` - 查看统计

### 管理员命令
- `/bw create <游戏名>` - 创建新游戏
- `/bw delete <游戏名>` - 删除游戏
- `/bw setspawn <游戏名> <队伍>` - 设置队伍出生点

## 汉化内容

### config.yml 汉化
- ✅ 所有配置项注释翻译为中文
- ✅ 默认语言设置为 `zh_CN`
- ✅ 资源名称汉化（青铜、铁锭、金锭）
- ✅ 游戏设置说明中文化

### shop.yml 汉化
- ✅ 商店分类名称汉化
- ✅ 物品名称和描述汉化
- ✅ 特殊物品功能说明汉化

### 界面汉化
- ✅ 游戏内消息中文化
- ✅ 商店界面中文化
- ✅ 统计信息中文化

## 版本信息

- **插件版本**: 1.3.6 汉化版
- **Minecraft版本**: 1.20+
- **Java版本**: 17+
- **构建日期**: 2025-06-18

## 技术支持

如果遇到问题，请检查：
1. 服务器版本是否为 1.20+
2. Java版本是否为 17+
3. 是否正确安装了依赖插件
4. 配置文件是否正确

## 许可证

本项目基于原始BedwarsRel项目，遵循GNU General Public License v3.0许可证。

## 致谢

- 感谢原始BedwarsRel项目的开发者
- 感谢Minecraft中文社区的支持
