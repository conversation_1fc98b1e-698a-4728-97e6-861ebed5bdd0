package io.github.bedwarsrel.commands;

import io.github.bedwarsrel.utils.MaterialCompatibility;

import com.google.common.collect.ImmutableMap;
import io.github.bedwarsrel.BedwarsRel;
import io.github.bedwarsrel.game.Game;
import io.github.bedwarsrel.game.GameState;
import io.github.bedwarsrel.game.Team;
import io.github.bedwarsrel.utils.ChatWriter;
import io.github.bedwarsrel.utils.Utils;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.material.Bed;

public class SetBedCommand extends BaseCommand implements ICommand {

  public SetBedCommand(BedwarsRel plugin) {
    super(plugin);
  }

  @Override
  public boolean execute(CommandSender sender, ArrayList<String> args) {
    if (!super.hasPermission(sender)) {
      return false;
    }

    Player player = (Player) sender;
    String team = args.get(1);

    Game game = this.getPlugin().getGameManager().getGame(args.get(0));
    if (game == null) {
      player.sendMessage(ChatWriter.pluginMessage(ChatColor.RED
          + BedwarsRel
          ._l(sender, "errors.gamenotfound", ImmutableMap.of("game", args.get(0).toString()))));
      return false;
    }

    if (game.getState() == GameState.RUNNING) {
      sender.sendMessage(
          ChatWriter.pluginMessage(ChatColor.RED + BedwarsRel
              ._l(sender, "errors.notwhilegamerunning")));
      return false;
    }

    Team gameTeam = game.getTeam(team);

    if (gameTeam == null) {
      player.sendMessage(
          ChatWriter.pluginMessage(ChatColor.RED + BedwarsRel._l(player, "errors.teamnotfound")));
      return false;
    }

    HashSet<Material> transparent = new HashSet<Material>();
    transparent.add(Material.AIR);

    Class<?> hashsetType = Utils.getGenericTypeOfParameter(player.getClass(), "getTargetBlock", 0);
    Method targetBlockMethod = null;
    Block targetBlock = null;

    // 1.7 compatible
    try {
      try {
        targetBlockMethod =
            player.getClass().getMethod("getTargetBlock", new Class<?>[]{Set.class, int.class});
      } catch (Exception ex) {
        BedwarsRel.getInstance().logError(ex);
        try {
          targetBlockMethod = player.getClass().getMethod("getTargetBlock",
              new Class<?>[]{HashSet.class, int.class});
        } catch (Exception exc) {
          BedwarsRel.getInstance().logError(exc);
          exc.printStackTrace();
        }
      }

      if (hashsetType.equals(Byte.class)) {
        targetBlock = (Block) targetBlockMethod.invoke(player, new Object[]{null, 15});
      } else {
        targetBlock = (Block) targetBlockMethod.invoke(player, new Object[]{transparent, 15});
      }

    } catch (Exception e) {
      BedwarsRel.getInstance().logError(e);
      e.printStackTrace();
    }

    Block standingBlock = player.getLocation().getBlock().getRelative(BlockFace.DOWN);

    if (targetBlock == null || standingBlock == null) {
      player.sendMessage(
          ChatWriter.pluginMessage(ChatColor.RED + BedwarsRel._l(player, "errors.bedtargeting")));
      return false;
    }

    Material targetMaterial = game.getTargetMaterial();

    // 检查目标方块或站立方块是否为床或目标材料
    boolean isTargetBlockValid = targetBlock.getType() == targetMaterial || MaterialCompatibility.isBedMaterial(targetBlock.getType());
    boolean isStandingBlockValid = standingBlock.getType() == targetMaterial || MaterialCompatibility.isBedMaterial(standingBlock.getType());

    if (!isTargetBlockValid && !isStandingBlockValid) {
      player.sendMessage(
          ChatWriter.pluginMessage(ChatColor.RED + BedwarsRel._l(player, "errors.bedtargeting")));
      return false;
    }

    Block theBlock = null;
    if (isTargetBlockValid) {
      theBlock = targetBlock;
    } else {
      theBlock = standingBlock;
    }

    // 检查是否为床方块（支持1.20的所有床类型）
    if (MaterialCompatibility.isBedMaterial(theBlock.getType())) {
      Block neighbor = null;

      try {
        // 尝试使用1.20的床数据处理方式
        if (theBlock.getBlockData() instanceof org.bukkit.block.data.type.Bed) {
          org.bukkit.block.data.type.Bed bedData = (org.bukkit.block.data.type.Bed) theBlock.getBlockData();

          if (bedData.getPart() == org.bukkit.block.data.type.Bed.Part.FOOT) {
            // 脚部，需要找到头部
            neighbor = theBlock;
            theBlock = Utils.getBedNeighbor(neighbor);
          } else {
            // 头部，需要找到脚部
            neighbor = Utils.getBedNeighbor(theBlock);
          }
        } else {
          // 回退到旧版本的处理方式
          Bed theBed = (Bed) theBlock.getState().getData();

          if (!theBed.isHeadOfBed()) {
            neighbor = theBlock;
            theBlock = Utils.getBedNeighbor(neighbor);
          } else {
            neighbor = Utils.getBedNeighbor(theBlock);
          }
        }
      } catch (Exception e) {
        // 如果出现异常，使用简单的邻居查找
        neighbor = Utils.getBedNeighbor(theBlock);
      }

      // 检查是否成功找到邻居方块
      if (neighbor == null) {
        player.sendMessage(
            ChatWriter.pluginMessage(ChatColor.RED + "无法找到床的另一半！请确保床是完整的。"));
        return false;
      }

      gameTeam.setTargets(theBlock, neighbor);
    } else {
      gameTeam.setTargets(theBlock, null);
    }

    player.sendMessage(ChatWriter.pluginMessage(ChatColor.GREEN + BedwarsRel
        ._l(player, "success.bedset",
            ImmutableMap
                .of("team", gameTeam.getChatColor() + gameTeam.getName() + ChatColor.GREEN))));
    return true;
  }

  @Override
  public String[] getArguments() {
    return new String[]{"game", "team"};
  }

  @Override
  public String getCommand() {
    return "setbed";
  }

  @Override
  public String getDescription() {
    return BedwarsRel._l("commands.setbed.desc");
  }

  @Override
  public String getName() {
    return BedwarsRel._l("commands.setbed.name");
  }

  @Override
  public String getPermission() {
    return "setup";
  }

}
