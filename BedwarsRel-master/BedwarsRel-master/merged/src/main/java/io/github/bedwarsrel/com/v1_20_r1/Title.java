package io.github.bedwarsrel.com.v1_20_r1;

import org.bukkit.entity.Player;

public class Title {

  public static void showSubTitle(Player player, String subTitle, double fadeIn, double stay,
      double fadeOut) {
    // 1.20版本使用Bukkit API发送标题
    try {
      player.sendTitle("", subTitle,
          (int) Math.round(fadeIn * 20),
          (int) Math.round(stay * 20),
          (int) Math.round(fadeOut * 20));
    } catch (Exception e) {
      // 如果API不可用，发送到聊天栏作为备选
      player.sendMessage(subTitle);
    }
  }

  public static void showTitle(Player player, String title, double fadeIn, double stay,
      double fadeOut) {
    // 1.20版本使用Bukkit API发送标题
    try {
      player.sendTitle(title, "",
          (int) Math.round(fadeIn * 20),
          (int) Math.round(stay * 20),
          (int) Math.round(fadeOut * 20));
    } catch (Exception e) {
      // 如果API不可用，发送到聊天栏作为备选
      player.sendMessage(title);
    }
  }

}
