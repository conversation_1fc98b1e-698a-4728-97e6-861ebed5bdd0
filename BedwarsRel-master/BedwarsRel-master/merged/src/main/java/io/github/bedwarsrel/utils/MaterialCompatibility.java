package io.github.bedwarsrel.utils;

import io.github.bedwarsrel.BedwarsRel;
import org.bukkit.DyeColor;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.Map;

/**
 * 材料兼容性工具类，用于处理不同Minecraft版本之间的材料差异
 * 特别是1.20版本的适配
 */
public class MaterialCompatibility {

    // 颜色材料映射缓存
    private static final Map<String, Material> COLORED_MATERIAL_CACHE = new HashMap<>();
    
    // 1.20版本检测
    private static Boolean isModernVersion = null;

    /**
     * 检查是否为现代版本（1.13+）
     */
    public static boolean isModernVersion() {
        if (isModernVersion == null) {
            try {
                String version = BedwarsRel.getInstance().getCurrentVersion();
                if (version != null) {
                    // 解析版本号
                    String[] parts = version.split("_");
                    if (parts.length >= 2) {
                        int majorVersion = Integer.parseInt(parts[1]);
                        isModernVersion = majorVersion >= 13;
                    } else {
                        isModernVersion = true; // 默认为现代版本
                    }
                } else {
                    isModernVersion = true;
                }
            } catch (Exception e) {
                isModernVersion = true; // 默认为现代版本
            }
        }
        return isModernVersion;
    }

    /**
     * 获取兼容的羊毛材料
     */
    public static Material getWoolMaterial(DyeColor color) {
        if (isModernVersion()) {
            // 1.13+版本使用新的材料名称
            String materialName = color.name() + "_WOOL";
            Material material = Material.getMaterial(materialName);
            if (material != null) {
                return material;
            }
        }
        
        // 回退到基础羊毛
        try {
            return Material.getMaterial("WOOL");
        } catch (Exception e) {
            return Material.WHITE_WOOL; // 1.13+的默认羊毛
        }
    }

    /**
     * 获取兼容的玻璃材料
     */
    public static Material getGlassMaterial(DyeColor color) {
        if (isModernVersion()) {
            String materialName = color.name() + "_STAINED_GLASS";
            Material material = Material.getMaterial(materialName);
            if (material != null) {
                return material;
            }
        }
        
        // 回退到基础玻璃
        try {
            return Material.getMaterial("STAINED_GLASS");
        } catch (Exception e) {
            return Material.GLASS;
        }
    }

    /**
     * 获取兼容的粘土材料
     */
    public static Material getClayMaterial(DyeColor color) {
        if (isModernVersion()) {
            String materialName = color.name() + "_TERRACOTTA";
            Material material = Material.getMaterial(materialName);
            if (material != null) {
                return material;
            }
        }
        
        // 回退到基础粘土
        try {
            return Material.getMaterial("STAINED_CLAY");
        } catch (Exception e) {
            return Material.TERRACOTTA;
        }
    }

    /**
     * 获取兼容的混凝土材料
     */
    public static Material getConcreteMaterial(DyeColor color) {
        if (isModernVersion()) {
            String materialName = color.name() + "_CONCRETE";
            Material material = Material.getMaterial(materialName);
            if (material != null) {
                return material;
            }
        }
        
        // 如果没有混凝土，回退到羊毛
        return getWoolMaterial(color);
    }

    /**
     * 获取兼容的床材料
     */
    public static Material getBedMaterial(DyeColor color) {
        if (isModernVersion()) {
            String materialName = color.name() + "_BED";
            Material material = Material.getMaterial(materialName);
            if (material != null) {
                return material;
            }
        }
        
        // 回退到基础床
        return getBedMaterial();
    }

    /**
     * 创建带颜色的物品
     */
    public static ItemStack createColoredItem(Material baseMaterial, DyeColor color, int amount) {
        try {
            Material coloredMaterial = null;
            
            if (baseMaterial.name().contains("WOOL")) {
                coloredMaterial = getWoolMaterial(color);
            } else if (baseMaterial.name().contains("GLASS")) {
                coloredMaterial = getGlassMaterial(color);
            } else if (baseMaterial.name().contains("CLAY") || baseMaterial.name().contains("TERRACOTTA")) {
                coloredMaterial = getClayMaterial(color);
            } else if (baseMaterial.name().contains("CONCRETE")) {
                coloredMaterial = getConcreteMaterial(color);
            } else if (baseMaterial.name().contains("BED")) {
                coloredMaterial = getBedMaterial(color);
            }
            
            if (coloredMaterial != null) {
                return new ItemStack(coloredMaterial, amount);
            }
            
            // 如果没有找到对应的彩色材料，返回原材料
            return new ItemStack(baseMaterial, amount);
        } catch (Exception e) {
            BedwarsRel.getInstance().logError(e);
            // 回退到基础材料
            return new ItemStack(baseMaterial, amount);
        }
    }

    /**
     * 获取床方块材料（兼容1.20）
     */
    public static Material getBedMaterial() {
        try {
            // 尝试获取BED_BLOCK（旧版本）
            Material bedBlock = Material.getMaterial("BED_BLOCK");
            if (bedBlock != null) {
                return bedBlock;
            }
        } catch (Exception e) {
            // 忽略异常
        }

        // 使用新版本的RED_BED作为默认床
        return Material.RED_BED;
    }

    /**
     * 获取生成蛋材料
     */
    public static Material getSpawnEggMaterial(String entityType) {
        if (isModernVersion()) {
            String materialName = entityType.toUpperCase() + "_SPAWN_EGG";
            Material material = Material.getMaterial(materialName);
            if (material != null) {
                return material;
            }
        }
        
        // 回退到通用生成蛋
        try {
            return Material.getMaterial("MONSTER_EGG");
        } catch (Exception e) {
            return Material.SHEEP_SPAWN_EGG; // 1.13+的默认生成蛋
        }
    }

    /**
     * 获取TNT材料
     */
    public static Material getTNTMaterial() {
        try {
            // 尝试获取旧版本的TNT
            Material tnt = Material.getMaterial("TNT");
            if (tnt != null) {
                return tnt;
            }
        } catch (Exception e) {
            // 忽略异常
        }

        // 使用新版本的TNT
        return Material.TNT;
    }

    /**
     * 获取兼容的材料映射
     */
    public static Material getCompatibleMaterial(String oldMaterialName) {
        // 材料名称映射表
        switch (oldMaterialName.toUpperCase()) {
            case "BED_BLOCK":
                return Material.RED_BED;
            case "BED":
                return Material.RED_BED;
            case "SKULL_ITEM":
                return Material.PLAYER_HEAD;
            case "SNOW_BALL":
                return Material.SNOWBALL;
            case "MONSTER_EGG":
                return Material.SHEEP_SPAWN_EGG;
            case "MONSTER_EGGS":
                return Material.INFESTED_STONE;
            case "STAINED_GLASS":
                return Material.WHITE_STAINED_GLASS;
            case "WOOL":
                return Material.WHITE_WOOL;
            case "STAINED_CLAY":
                return Material.WHITE_TERRACOTTA;
            case "CARPET":
                return Material.WHITE_CARPET;
            case "STAINED_GLASS_PANE":
                return Material.WHITE_STAINED_GLASS_PANE;
            case "SULPHUR":
                return Material.GUNPOWDER;
            case "EYE_OF_ENDER":
                return Material.ENDER_EYE;
            case "STATIONARY_WATER":
                return Material.WATER;
            case "STATIONARY_LAVA":
                return Material.LAVA;
            case "WEB":
                return Material.COBWEB;
            case "SOIL":
                return Material.FARMLAND;
            default:
                // 尝试直接获取材料
                Material material = Material.getMaterial(oldMaterialName);
                return material != null ? material : Material.STONE;
        }
    }

    /**
     * 检查材料是否为床方块（支持1.20的所有床类型）
     */
    public static boolean isBedMaterial(Material material) {
        if (material == null) {
            return false;
        }

        // 检查是否为任何颜色的床
        String materialName = material.name();
        return materialName.endsWith("_BED") ||
               materialName.equals("BED") ||
               materialName.equals("BED_BLOCK");
    }

    /**
     * 获取兼容的DyeColor
     */
    public static org.bukkit.DyeColor getCompatibleDyeColor(String colorName) {
        switch (colorName.toUpperCase()) {
            case "SILVER":
                return org.bukkit.DyeColor.LIGHT_GRAY;
            default:
                try {
                    return org.bukkit.DyeColor.valueOf(colorName.toUpperCase());
                } catch (Exception e) {
                    return org.bukkit.DyeColor.WHITE;
                }
        }
    }

    /**
     * 检查材料是否存在
     */
    public static boolean materialExists(String materialName) {
        try {
            return Material.getMaterial(materialName.toUpperCase()) != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查材料是否为羊毛类型
     */
    public static boolean isWoolMaterial(Material material) {
        if (material == null) {
            return false;
        }

        String materialName = material.name();
        return materialName.endsWith("_WOOL") || materialName.equals("WOOL");
    }

    /**
     * 从羊毛材料获取对应的染料颜色
     */
    public static DyeColor getDyeColorFromWoolMaterial(Material material) {
        if (material == null || !isWoolMaterial(material)) {
            return null;
        }

        String materialName = material.name();

        // 1.20版本的羊毛材料映射
        switch (materialName) {
            case "WHITE_WOOL":
            case "WOOL":
                return DyeColor.WHITE;
            case "ORANGE_WOOL":
                return DyeColor.ORANGE;
            case "MAGENTA_WOOL":
                return DyeColor.MAGENTA;
            case "LIGHT_BLUE_WOOL":
                return DyeColor.LIGHT_BLUE;
            case "YELLOW_WOOL":
                return DyeColor.YELLOW;
            case "LIME_WOOL":
                return DyeColor.LIME;
            case "PINK_WOOL":
                return DyeColor.PINK;
            case "GRAY_WOOL":
                return DyeColor.GRAY;
            case "LIGHT_GRAY_WOOL":
                return DyeColor.LIGHT_GRAY;
            case "CYAN_WOOL":
                return DyeColor.CYAN;
            case "PURPLE_WOOL":
                return DyeColor.PURPLE;
            case "BLUE_WOOL":
                return DyeColor.BLUE;
            case "BROWN_WOOL":
                return DyeColor.BROWN;
            case "GREEN_WOOL":
                return DyeColor.GREEN;
            case "RED_WOOL":
                return DyeColor.RED;
            case "BLACK_WOOL":
                return DyeColor.BLACK;
            default:
                return null;
        }
    }

    /**
     * 从染料颜色获取对应的羊毛材料
     */
    public static Material getWoolMaterialByDyeColor(DyeColor dyeColor) {
        if (dyeColor == null) {
            return Material.WHITE_WOOL;
        }

        switch (dyeColor) {
            case WHITE:
                return Material.WHITE_WOOL;
            case ORANGE:
                return Material.ORANGE_WOOL;
            case MAGENTA:
                return Material.MAGENTA_WOOL;
            case LIGHT_BLUE:
                return Material.LIGHT_BLUE_WOOL;
            case YELLOW:
                return Material.YELLOW_WOOL;
            case LIME:
                return Material.LIME_WOOL;
            case PINK:
                return Material.PINK_WOOL;
            case GRAY:
                return Material.GRAY_WOOL;
            case LIGHT_GRAY:
                return Material.LIGHT_GRAY_WOOL;
            case CYAN:
                return Material.CYAN_WOOL;
            case PURPLE:
                return Material.PURPLE_WOOL;
            case BLUE:
                return Material.BLUE_WOOL;
            case BROWN:
                return Material.BROWN_WOOL;
            case GREEN:
                return Material.GREEN_WOOL;
            case RED:
                return Material.RED_WOOL;
            case BLACK:
                return Material.BLACK_WOOL;
            default:
                return Material.WHITE_WOOL;
        }
    }
}
