package io.github.bedwarsrel.listener;

import io.github.bedwarsrel.BedwarsRel;
import io.github.bedwarsrel.game.Game;
import io.github.bedwarsrel.game.GameState;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.world.ChunkUnloadEvent;

public class <PERSON>k<PERSON><PERSON><PERSON> implements Listener {

  @EventHandler
  public void onUnload(ChunkUnloadEvent unload) {
    Game game = BedwarsRel.getInstance().getGameManager()
        .getGameByChunkLocation(unload.getChunk().getX(),
            unload.getChunk().getZ());
    if (game == null) {
      return;
    }

    if (game.getState() != GameState.RUNNING) {
      return;
    }

    // 1.20版本中ChunkUnloadEvent不再支持setCancelled()
    // 我们需要使用其他方法来保持区块加载
    // unload.setCancelled(true);
  }

}
