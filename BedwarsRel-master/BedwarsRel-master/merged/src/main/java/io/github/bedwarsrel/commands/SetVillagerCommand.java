package io.github.bedwarsrel.commands;

import com.google.common.collect.ImmutableMap;
import io.github.bedwarsrel.BedwarsRel;
import io.github.bedwarsrel.game.Game;
import io.github.bedwarsrel.utils.ChatWriter;
import java.util.ArrayList;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.entity.Villager;

public class SetVillagerCommand extends BaseCommand implements ICommand {

  public SetVillagerCommand(BedwarsRel plugin) {
    super(plugin);
  }

  @Override
  public boolean execute(CommandSender sender, ArrayList<String> args) {
    if (!sender.hasPermission("bw." + this.getPermission())) {
      return false;
    }

    Player player = (Player) sender;
    Game game = this.getPlugin().getGameManager().getGame(args.get(0));

    if (game == null) {
      sender.sendMessage(ChatWriter.pluginMessage(ChatColor.RED
          + BedwarsRel
          ._l(sender, "errors.gamenotfound", ImmutableMap.of("game", args.get(0).toString()))));
      return false;
    }

    if (game.getState() != io.github.bedwarsrel.game.GameState.STOPPED) {
      player.sendMessage(
          ChatWriter.pluginMessage(ChatColor.RED + "游戏必须处于停止状态才能设置村民商店！当前状态: " + game.getState()));
      player.sendMessage(
          ChatWriter.pluginMessage(ChatColor.YELLOW + "请等待游戏结束或使用 /bw stop " + game.getName() + " 停止游戏"));
      return false;
    }

    Location location = player.getLocation();
    
    // 在指定位置生成村民
    Villager villager = (Villager) location.getWorld().spawnEntity(location, EntityType.VILLAGER);
    villager.setCustomName(ChatColor.GREEN + "商店");
    villager.setCustomNameVisible(true);
    villager.setAI(false); // 禁用AI，防止村民移动
    villager.setSilent(true); // 静音
    villager.setInvulnerable(true); // 无敌
    
    // 设置村民职业为无职业，避免自动交易
    villager.setProfession(Villager.Profession.NONE);
    
    player.sendMessage(
        ChatWriter.pluginMessage(ChatColor.GREEN + BedwarsRel._l(player, "success.villagerset",
            ImmutableMap.of("game", game.getName()))));
    
    player.sendMessage(
        ChatWriter.pluginMessage(ChatColor.YELLOW + "村民商店已在当前位置创建！"));
    player.sendMessage(
        ChatWriter.pluginMessage(ChatColor.YELLOW + "请确保在 shop.yml 中配置了商店物品！"));
    
    return true;
  }

  @Override
  public String[] getArguments() {
    return new String[]{"game"};
  }

  @Override
  public String getCommand() {
    return "setvillager";
  }

  @Override
  public String getDescription() {
    return BedwarsRel._l("commands.setvillager.desc");
  }

  @Override
  public String getName() {
    return BedwarsRel._l("commands.setvillager.name");
  }

  @Override
  public String getPermission() {
    return "setup";
  }

}
