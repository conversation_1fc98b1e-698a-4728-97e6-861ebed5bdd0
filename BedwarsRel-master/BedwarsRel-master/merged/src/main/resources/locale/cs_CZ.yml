---
default:
  pages: "Stranka $current$ z $max$"
  currently: Aktualni
errors:
  argumentslength: "<PERSON><PERSON> z<PERSON> počet argumentů!"
  holodependencynotfound: "Potrebny plugin $dependency$ nenalezen. Bez nej nelze spustit Hologram-statistiky"
  packagenotfound: "Nelze nacist balicek $package$!"
  classnotfound: "Nelze nacist balicek $package$ ve tride $class$!"
  gameexists: "Hra se stejnym jmenem jiz byla vytvorena!"
  gamenotfound: "Hra '$game$' neexistuje!"
  nofreegames: "Aktualne nejsou volne zadne hry."
  gamenotfoundsimple: "Hra neexistuje!"
  playeramount: "Pocet hracu musi byt v rozsahu 1 az 24!"
  teamcolornotallowed: "Tato barva neni pro tymy povolena."
  teamnamelength: "Nazev tymu musi byt v rozsahu 2 - 20!"
  teamnotfound: "Tym nenalezen!"
  notingame: "Prikaz nebyl zadan ve hre!"
  bedtargeting: "Musis stat na bloku, ktery je v konfiguraci zapsany jako 'game-block'!"
  regionargument: "Argumenty u regionu jsou pouze 'loc1' nebo 'loc2'!"
  spawnerargument: "Parametr spawneru musi byt dobre nakonfigurovan!"
  blockdownnotfound: "Nebyl nalezen block na kterem stojis!"
  gamenotrunning: "Hra neni spustena!"
  bungeenoserver: "BungeeCord server neni spravne nastaven! Kontaktuj administratora!"
  cantstartagain: "Hra jiz bezi! Nelze zapnout hru, ktera jiz bezi!"
  startoutofwaiting: "Hra bude zapnuta bez cekani!"
  cantjoingame: "Nemuzes se pripojit do hry, ktera jiz bezi nebo je vypnuta!"
  lobbyongameworld: "Lobby nemuze byt na hernim svete!"
  gamenotloaded: "Hru nelze nacist!"
  gameloaderror: "Nelze nacist hru '$game$'!"
  regionnotfound: "Soubor mapy neexistuje!"
  savesign: "Nelze vytvorit novou konfiguraci pro cedulku!"
  nogames: "Aktualne nejsou nakonfigurovany zadne areny.."
  notenoughress: "Nemas dostatek surovin pro koupi tohoto predmetu!"
  teamnameinuse: "Jmeno tymu je jiz pouzito!"
  minplayersmustnumber: "Minimalni pocet hracu musi byt cislo!"
  toolongregionname: "Maximalni delka muze byt 15 pismen dlouha!"
  notwhilegamerunning: "Nelze provest pokud hra bezi!"
  notwhileingame: "Nelze provest pokud jsi ve hre."
  timeincorrect: "Cas musi byt v rozmezi 0 - 23000, day nebo night!"
  minplayersnumeric: "Pocet minimalnich hracu musi byt cislo!"
  notinair: "Nejsi ve vzduchu!"
  teamfull: "Tym je plny, otevri si znovu vyberove menu!"
  novalidmaterial: "Blok (material) neni spravny!"
  wrongvalueonoff: "Spatny parametr! Pouzij true nebo on nebo 1 pro zapnuti - Pouzij false nebo off nebo 0 pro vypnuti!"
  playernotfound: "Hrac nebyl nalezen!"
  notingameforkick: "Hrace lze vyhodit pouze ze hry!"
  playernotingame: "Zadany hrac se nenachazi ve hre!"
  mustbeinlobbyworld: "Musis byt v hernim lobby!"
  addteamjoincancel: "Pridavani pripojeni do hry zruseno!"
  entitynotcompatible: "Tuto entitu nelze pouzit pro vyber tymu!"
success:
  gameadded: "Nova hra '$game$' uspesne pridana!"
  teamadded: "Tym '$team$' uspesne pridan!"
  joined: "Pripojeni do hry bylo uspesne!"
  left: "Odpojeni ze hry bylo uspesne."
  saved: "Hra byla uspesne ulozena!"
  bedset: "Uspesne jsi nastavil respawn blok tymu $team$!"
  regionset: "Oblast $location$ pro hru $game$ uspesne nastavena!"
  spawnset: "Spawn lokace pro tym $team$ uspesne nastavena!"
  spawnerset: "Spawn surovin $name$ uspesne nastaven!"
  stopped: "Hra byla zastavena!"
  lobbyset: "Lobby pro hru bylo nastaveno!"
  gameloaded: "Hra '$game$' uspesne nactena!"
  reloadconfig: "Konfigurace a jazyky znovu nacteny!"
  teamremoved: "Tyym byl uspesne odstranen!"
  gameremoved: "Hra byla uspesne odstranena!"
  spawnercleared: "Vsechny spawnery surovin byly odstraneny!"
  gamerun: "Hra byla zapnuta, hraci jiz mohou hrat!"
  timeset: "Cas hry byl uspesne nastaven!"
  regionnameset: "Jmeno regionu uspesne nastaveno!"
  minplayersset: "Min. pocet hracu nastaven!"
  mainlobbyset: "Mainlobby uspesne nastaveno!"
  gametimeset: "Cas hry uspesne nastaven!"
  materialset: "Respawn blok (material) byl uspesne nastaven!"
  builderset: "Stavitel teto mapy byl uspesne nastaven a bude se zobrazovat v title!"
  autobalanceseton: "Automaticke vyvazovani bylo &lzapnuto&a!"
  autobalancesetoff: "Automaticke vyvazovani bylo &cvypnuto&a!"
  selectteamjoinentity: "Nyni klikni pravym na entitu, kterou chces pouzit!"
  teamjoinadded: "Entita byla uspesne ulozena pro tym $team$"
  holoremoved: "Hologram uspesne odstranen!"
gamecheck:
  LOC_NOT_SET_ERROR: "Lokace pro tuto hru nebyla dobre nastavena!"
  TEAM_SIZE_LOW_ERROR: "Musis nastavit vice tymu!"
  NO_RES_SPAWNER_ERROR: "Nenastavil jsi zadny spawner surovin!"
  NO_LOBBY_SET: "Nenastavil jsi lobby!"
  TEAMS_WITHOUT_SPAWNS: "Jsou zde tymy bez spawn lokace!"
  NO_ITEMSHOP_CATEGORIES: "Zadne kategorie obchodu nenalezeny!"
  NO_MAIN_LOBBY_SET: "Nenastavil jsi Main lobby (hlavni lobby) nebo jsi v nastaveni nepovolil 'tomainlobby' na true (povoleno)"
  TEAM_NO_WRONG_BED: "Jeden nebo vice tymu nemaji nastavenou postel!"
  TEAM_NO_WRONG_TARGET: "Jeden nebo vice tymu nemaji nastaveny respawn block!"
ingame:
  team: "Tým"
  teams: "Tymy"
  all: "Vsichni"
  record: "&e$record$&a je rekord na teto mape!"
  record-with-holders: '&aRekord na této mape je &e$record$&a ziskany hracmi: $holders$'
  newrecord: '&aTym $team$&a ziskal novy rekord: &6$record$'
  record-nobeddestroy: "&cRekord nemohl byt ulozen, jelikoz &lnebyla &cznicena zadna postel!"
  teamwon: "Gratulace! Tym $team$ vyhral!"
  draw: "Hra skoncila remizou."
  serverrestart: "Restart serveru za $sec$ sekund!"
  gamestarting: "Hra se zapina!"
  gamestarted: "Hra '$game$' zacala!"
  backtolobby: "Teleportace do lobby za $sec$ sekund!"
  spectator: "Sledujici"
  spectate: "&aSledovat"
  teamchest: "Tymova truhla"
  noturteamchest: "Tato truhla nepatri tvemu tymu!"
  protectionleft: "Jsi nezranitelny na &c$length$&f sekund!"
  protectionend: "Nyni jsi opet &czranitelny&f!"
  team-dead: "Tym $team$ byl znicen!"
  no-friendlybreak: "&cNemuzes znicit blok pod hracem sveho tymu!"
  teamchestdestroy: "&cTymova truhla byla znicena!"
  title:
    map-builder: "Mapu postavil/a $builder$"
    win-title: "&a&lGratulace!"
    win-subtitle: "$team$&6 vyhral v case &e$time$"
  shop:
    name: "Obchod"
    newshop: "Pouzit novy obchod"
    oldshop: "Pouzit stary obchod"
    fullstackpershift: "Vice stacku za shift click"
    onestackpershift: "Jeden stack za shift click"
  player:
    left: "Hrac $player$ odesel ze hry."
    died: "$player$ zemrel!"
    killed: "$killer$ zabil $player$!"
    kicked: "$player$ byl vyhozen!"
    waskicked: "Byl jsi vyhozen ze hry!"
  blocks:
    ownbeddestroy: "Nemuzes znicit svou postel!"
    beddestroyed: "$player$ znicil postel tymu $team$!"
  specials:
    rescue-platform:
      left: "&cMusis pockat &e$time$&f sekund(u) pred pouzitim dalsi zachrane platformy!"
    arrow-blocker:
      start: "Máš ochranu proti zásahu šípem na &c$time$&f sekund."
      end: "&cOchrana proti zásahu šípem skončila"
      left: "Musíš počkat ještě &c$time$&f sekund než budeš moci znovu použít ochranu proti šípům!"
    trap:
      trapped: "&eNekdo vstoupil do &cpasti&e tveho tymu!"
    protection-wall:
      left: "Musis pockat &c$time$&f sekund pred pouzitim!"
      not-usable-here: "Tento predmet zde nemuzes pouzit!"
    warp-powder:
      cancelled: "&cTeleportace zrusena!"
      start: "Budes teleportovan za &c$time$&f sekund!"
      cancel: "&4Zrusit Teleport"
      multiuse: "&cTeleportace probiha! Nelze se teleportovat znova."
    tntsheep:
      no-target-found: "Nebyl nalezen zadny cil!"
    tracker:
      no-target-found: "Nebyl nalezen zadny cil!"
      target-found: "$player$ je $blocks$ bloku od tebe."
lobby:
  countdown: "Hra zacne za $sec$ sekund!"
  countdowncancel: "Je potreba vice hracu! Odpocet byl zastaven."
  cancelcountdown:
    not_enough_players: "Je potreba vice hracu! Odpocet byl zastaven."
    not_enough_teams: "Je potreba vice tymu. Odpocitavani bylo zastaveno!"
  cancelstart:
    not_enough_players: "Pro start hry je potreba vice hracu!"
    not_enough_teams: "Je potreba vice tymu pro start hry!"
  chooseteam: "Vyber si tym"
  startgame: "start"
  reduce_countdown: "Snížit odpočítávání"
  gamefull: "Hra je plna!"
  gamefullpremium: "Tato hra je jiz plna VIP hracu!"
  teamjoined: "Uspesne jsi se pripojil do tymu $team$"
  leavegame: "Opustit hru"
  playerjoin: "Hrac $player$ se pripojil do hry!"
  kickedbyvip: "Bylo uvolneno misto pro VIP hrace, byl(a) jsi vyhozen."
  moreplayersneeded: "Je potreba $count$ hracu navic."
  moreplayersneeded-one: "Je potreba $count$ hrac navic."
  moreteamsneeded: "Jsou potreba minimalne dva hraci ve dvou ruznych tymech pro start hry!"
sign:
  firstline: "&6[Bedwars]"
  players: "Hraci"
  gamestate:
    stopped: "&4ZASTAVENO"
    waiting: "&aLOBBY"
    running: "&9HRA PROBIHA!"
    full: "&cPLNO!"
stats:
  header: "Statistiky BedWars her"
  kd: "K/D"
  statsnotfound: "Statistika hrace $player$ nenalezena!"
  name: "Jmeno"
  kills: "Zabiti"
  deaths: "Smrti"
  wins: "Vyhry"
  loses: "Prohry"
  score: "Skore"
  destroyedBeds: "Znicene postele"
  games: "Hrane hry"
commands:
  addgame:
    name: "addgame"
    desc: "Prida novou hru"
  addteam:
    name: "addteam"
    desc: "Prida tym pro urcenou arenu"
  join:
    name: "join"
    desc: "Prikaz pro pripojeni do urcite areny"
  leave:
    name: "leave"
    desc: "Prikaz pro odpojeni z urcite areny"
  save:
    name: "save"
    desc: "Ulozi parametry areny"
  settarget:
    name: "settarget"
    desc: "Nastavi lokaci bloku tymu"
  setbed:
    name: "setbed (stejne jako settarget)"
    desc: "Nastavi lokaci postele tymu (stejne jako settarget)"
  setlobby:
    name: "setlobby"
    desc: "Nastavi lokaci herniho lobby"
  setregion:
    name: "setregion"
    desc: "Nastavi hranice hry, po ktere jde stavet, etc"
  setspawn:
    name: "setspawn"
    desc: "Nastavi misto, na kterem se bude objevovat tym"
  setspawner:
    name: "setspawner"
    desc: "Nastavi misto pro spawnovani surovin"
  start:
    name: "start"
    desc: "Spusti hru"
  stop:
    name: "stop"
    desc: "Ukonci hru"
  help:
    name: "help"
    desc: "Ukaze veskere potrebne informace"
  reload:
    name: "reload"
    desc: "Znovu nacte veskerou konfiguraci a jazyky"
  setmainlobby:
    name: "setmainlobby"
    desc: "Nastavi tzv. main lobby, kam se teleportuji hraci po odehrane hre. (je potreba pokud je mainlobby-enabled nastaveno na true)"
  list:
    name: "list"
    desc: "Zobrazi veskere dostupne hry"
  regionname:
    name: "regionname"
    desc: "Nastavi jmeno pro arenu"
  removeteam:
    name: "removeteam"
    desc: "Smaze tym ze hry (pouze pokud je hra vypnuta)"
  removegame:
    name: "removegame"
    desc: "Smaze hru a konfiguraci"
  clearspawner:
    name: "clearspawner"
    desc: "Odstrani vsechny spawnery surovin. Je nutne ulozit opet arenu."
  gametime:
    name: "gametime"
    desc: "Nastavi cas hry, po ktery se hraje v arene"
  stats:
    name: "stats"
    desc: "Zobrazi kompletni vypis BedWars statistik"
  setbuilder:
    name: "setbuilder"
    desc: "Nastavi autora mapy, ktery se bude zobrazovat v titulu."
  setgameblock:
    name: "setgameblock"
    desc: "Nastavi block hry, ktery muze byt pouzit misto bude pouzit misto 'game-block' pro tuto arenu. Parametr 'DEFAULT' pouzije blok z konfiguracniho souboru."
  setautobalance:
    name: "setautobalance"
    desc: "Pokud je parametr 'global-autobalance' nastaven na 'false', tak lze pomoci tohoto prikazu nastavit automaticke balancovani hracu pro tuto hru!"
  setminplayers:
    name: "Nastavit minimum hracu"
    desc: "Nastavit minimalni pocet hracu pro start hry"
  kick:
    name: "kick"
    desc: "Vyhodi zadaneho hrace ze hry"
  addteamjoin:
    name: "addteamjoin"
    desc: "Vybere entitu, pomoci ktere se budete moci pripojit do tymu!"
  addholo:
    name: "addholo"
    desc: "Prida hologram do aktualni pozice!"
  removeholo:
    name: "removeholo"
    desc: "Pri zadani prikazu staci kliknou pravym tlacitkem pro odstraneni.."
    explain: "Uvnitr hologramu drz 10 sekund prave tlacitko!"
  debugpaste:
    name: "Vložení debug dat"
    desc: "Odešle pár debug dat na hastebin a vytvoří Url link, který můžete sdílet s vývojáři"
  itemspaste:
    name: "Vložte váš inventář do souboru"
    desc: "Toto navrátí zpět odkaz, kde můžete vidět váš momentální serializovaný inventář jako příklad pro váš soubor shop.yml"
