---
default:
  pages: "Seite $current$ von $max$"
  currently: Aktuell
errors:
  argumentslength: "Die Anzahl der Argumente entspricht nicht der Vorgabe!"
  holodependencynotfound: "Es fehlt $dependency$ für die Hologram-Statistik"
  packagenotfound: "Konnte Package $package$ nicht laden!"
  classnotfound: "Konnte $package$ Klasse $class$ nicht laden!"
  gameexists: "Ein Spiel mit diesem Namen existiert bereits!"
  gamenotfound: "Das Spiel '$game$' konnte nicht gefunden werden!"
  nofreegames: "Es wurde kein verfügbares Spiel gefunden."
  gamenotfoundsimple: "Spiel nicht gefunden!"
  playeramount: "Die maximale Anzahl kann nicht weniger als 1 und nicht größer als 24 sein!"
  teamcolornotallowed: "Die angegebene Teamfarbe ist nicht erlaubt!"
  teamnamelength: "Der Teamname muss zwischen 2 und 20 Zeichen haben!"
  teamnotfound: "Team nicht gefunden!"
  notingame: "Du bist zurzeit in keinem Spiel!"
  bedtargeting: "Du musst ein Respawn-Block des konfigurierten 'game-block' anvisieren oder auf einem stehen!"
  regionargument: "Dein Location-Parameter muss 'loc1' oder 'loc2' entsprechen!"
  spawnerargument: "Dein Ressourcen-Parameter ist ungültig oder verweist auf keine konfigurierte Ressource!"
  blockdownnotfound: "Der Block unter dir konnte nicht gefunden werden!"
  gamenotrunning: "Spiel läuft nicht!"
  bungeenoserver: "Der Bungeecord Server wurde nicht korrekt konfiguriert. Bitte benachrichtige den Serveradministrator!"
  cantstartagain: "Spiel läuft! Du kannst kein laufendes Spiel starten!"
  startoutofwaiting: "Ein Spiel muss aus dem Lobby-Status gestartet werden!"
  cantjoingame: "Du kannst keinem laufenden oder beendetem Spiel beitreten!"
  lobbyongameworld: "Die Lobby darf nicht auf der Welt des Spiels sein!"
  gamenotloaded: "Spiel konnte nicht geladen/gestartet werden!"
  gameloaderror: "Das Laden des Spiels '$game$' hat einen Fehler verursacht!"
  regionnotfound: "Region-Datei existiert nicht!"
  savesign: "Speichern der Schilder nicht möglich (Datei konnte nicht erstellt werden)!"
  nogames: "Keine Spiele gefunden!"
  notenoughress: "Du hast nicht genug Ressourcen um das zu kaufen!"
  teamnameinuse: "Ein Team mit diesem Namen existiert bereits!"
  minplayersmustnumber: "Die min. Spieler muss eine Nummer sein!"
  toolongregionname: "Region-Name darf nur maximal 15 Zeichen haben!"
  notwhilegamerunning: "Das kannst du nicht tun während das Spiel läuft!"
  notwhileingame: "Das kannst du nicht tun während du in einem Spiel bist!"
  timeincorrect: "Die Zeit muss eine Zahl sein (0 ... 23000), 'day' oder 'night'"
  minplayersnumeric: "Die Mindestanzahl der Spieler muss numerisch sein!"
  notinair: "Du bist nicht in der Luft!"
  teamfull: "Das Team ist voll, bitte öffne die Teamauswahl erneut!"
  novalidmaterial: "Angegebene Block-Typ (Material) ist nicht korrekt!"
  wrongvalueonoff: "Falscher Parameter! Einschalten mit: true,on,1 - Ausschalten mit: false,off,0"
  playernotfound: "Angegebener Spieler wurde nicht gefunden oder ist nicht online!"
  notingameforkick: "Du musst in einem Spiel sein, um kicken zu können!"
  playernotingame: "Angegebener Spieler ist nicht in diesem Spiel!"
  mustbeinlobbyworld: "Du musst in der Lobby-Welt des Spiels sein!"
  addteamjoincancel: "Das Hinzufügen von einem Team-Beitritt wurde abgebrochen!"
  entitynotcompatible: "Diese Kreature wird für die Team-Auswahl nicht unterstützt!"
success:
  gameadded: "Neues Spiel '$game$' erfolgreich hinzugefügt!"
  teamadded: "Team $team$ erfolgreich hinzugefügt!"
  joined: "Du bist dem Spiel beigetreten!"
  left: "Du hast das Spiel erfolgreich verlassen!"
  saved: "Spiel wurde erfolgreich gespeichert!"
  bedset: "Der Respawn-Block für Team $team$ wurde erfolgreich gesetzt!"
  regionset: "Der Regionspunkt $location$ wurde für das Spiel $game$ erfolgreich gesetzt!"
  spawnset: "Spawn-Punkt für Team $team$ wurde erfolgreich gesetzt!"
  spawnerset: "Ressourcen-Spawner $name$ wurde erfolgreich gesetzt!"
  stopped: "Spiel wurde erfolgreich gestoppt!"
  lobbyset: "Lobby wurde erfolgreich gesetzt!"
  gameloaded: "Spiel '$game$' wurde erfolgreich geladen!"
  reloadconfig: "Neu laden erfolgreich!"
  teamremoved: "Team wurde erfolgreich entfernt!"
  gameremoved: "Spiel wurde erfolgreich entfernt!"
  spawnercleared: "Alle Ressourcen-Spawner wurden erfolgreich entfernt!"
  gamerun: "Du hast das Spiel gestartet, Spieler können nun beitreten!"
  timeset: "Spiel-Zeit wurde erfolgreich gesetzt!"
  regionnameset: "Name der Region wurde erfolgreich festgelegt!"
  minplayersset: "Die Mindestanzahl der Spieler wurde erfolgreich gesetzt!"
  mainlobbyset: "Mainlobby wurde erfolgreich gesetzt!"
  gametimeset: "Die Spiel-Zeit wurde erfolgreich gesetzt!"
  materialset: "Der Respawn-Block Typ wurde erfolgreich für dieses Spiel gesetzt!"
  builderset: "Der Builder der Map wurde erfolgreich gesetzt!"
  autobalanceseton: "Autobalance wurde erfolgreich eingeschaltet!"
  autobalancesetoff: "Autobalance wurde erfolgreich &causgeschaltet&a!"
  selectteamjoinentity: "Mache nun einen Rechtsklick auf die Kreatur, die als Teambeitritt dienen soll!"
  teamjoinadded: "Kreatur wurde erfolgreich für die Team-Auswahl für das Team $team$ markiert!"
  holoremoved: "Hologram-Statistik erfolgreich entfernt!"
gamecheck:
  LOC_NOT_SET_ERROR: "Die Regionspunkte wurden nicht korrekt gesetzt!"
  TEAM_SIZE_LOW_ERROR: "Du musst mehr Teams hinzufügen!"
  NO_RES_SPAWNER_ERROR: "Du hast keinen Ressourcen-Spawner gesetzt!"
  NO_LOBBY_SET: "Es wurde keine Lobby gesetzt!"
  TEAMS_WITHOUT_SPAWNS: "Ein oder mehrere Teams haben keinen Spawn-Punkt!"
  NO_ITEMSHOP_CATEGORIES: "Shop hat keine Kategorien/Angebote gefunden!"
  NO_MAIN_LOBBY_SET: "Du hast keine Main-Lobby gesetzt, obwohl 'tomainlobby' auf true gesetzt wurde!"
  TEAM_NO_WRONG_BED: "Ein oder mehrere Teams haben kein Bett gesetzt bekommen!"
  TEAM_NO_WRONG_TARGET: "Ein oder mehrere Teams haben kein Respawn-Block gesetzt bekommen!"
ingame:
  team: "Team"
  teams: "Teams"
  all: "Alle"
  record: "&aDer Rekord auf dieser Map liegt bei &e$record$"
  record-with-holders: '&aDer Rekord liegt bei &e$record$&a und wird gehalten von: $holders$'
  newrecord: '&aDas Team $team$&a hat einen neuen Rekord aufgestellt: &6$record$'
  record-nobeddestroy: "&cRekord wird nicht gespeichert, weil kein Bett zerstört wurde!"
  teamwon: "Glückwunsch! Team $team$ hat gewonnen!"
  draw: "Das Spiel endet mit einem Unentschieden!"
  serverrestart: "Server-Neustart in $sec$ Sekunde(n)!"
  gamestarting: "Spiel startet ..."
  gamestarted: "Das Spiel '$game$' hat begonnen!"
  backtolobby: "Zurück zur Lobby in $sec$ Sekunde(n)"
  spectator: "Zuschauer"
  spectate: "&aZuschauen"
  teamchest: "Teamkiste"
  noturteamchest: "Diese Kiste gehört nicht deinem Team!"
  protectionleft: "Unverwundbar für &c$length$&f Sekunde(n)!"
  protectionend: "Du bist wieder &cverwundbar&f!"
  team-dead: "Das Team $team$ wurde vernichtet!"
  no-friendlybreak: "&cBlock unter deinem Team-Mitglied kann nicht zerstört werden!"
  teamchestdestroy: "&cEine eurer Teamkiste(n) wurde zerstört!"
  title:
    map-builder: "Gebaut von $builder$"
    win-title: "&6Glückwunsch!"
    win-subtitle: "&6Team $team$ &6hat in &e$time$ &6gewonnen!"
  shop:
    name: "Itemshop"
    newshop: "Neuen Shop verwenden"
    oldshop: "Alten Shop verwenden"
    fullstackpershift: "Mehrere Stacks bei Shift-Klick"
    onestackpershift: "Ein Stack bei Shift-Klick"
  player:
    left: "Spieler $player$ hat das Spiel verlassen!"
    died: "$player$ ist gestorben!"
    killed: "$player$ wurde von $killer$ getötet!"
    kicked: "$player$ wurde aus dem Spiel gekickt!"
    waskicked: "Du wurdest aus dem Spiel gekickt!"
  blocks:
    ownbeddestroy: "Du kannst dein eigenes Bett nicht zerstören!"
    beddestroyed: "Das Bett von Team $team$ wurde von $player$ zerstört!"
  specials:
    rescue-platform:
      left: "Du musst noch &c$time$&f Sekunde(n) warten, um die nächste Rettungsplattform zu aktivieren!"
    arrow-blocker:
      start: "Du bist nun für &c$time$&f Sekunde(n) vor Pfeilen geschützt."
      end: "&cDein Schutz gegen Pfeile ist nun abgelaufen"
      left: "Du kannst den Schutz vor Pfeilen erst in &c$time$&f Sekunde(n) wieder nutzen!"
    trap:
      trapped: "&eJemand ist in eine &cTrap&e von eurem Team gelaufen!"
    protection-wall:
      left: "Du musst noch &c$time$&f Sekunde(n) warten, um die nächste Schutzmauer zu aktivieren!"
      not-usable-here: "Du kannst die Schutzmauer hier nicht verwenden!"
    warp-powder:
      cancelled: "&cDeine Teleportation wurde abgebrochen."
      start: "Du wirst in &c$time$&f Sekunde(n) teleportiert. Bewege dich nicht."
      cancel: "&4Teleportation abbrechen"
      multiuse: "&cDu hast bereits eine Teleportation gestartet!"
    tntsheep:
      no-target-found: "Es wurde kein Zielspieler gefunden!"
    tracker:
      no-target-found: "Es wurde kein Zielspieler gefunden!"
      target-found: "$player$ ist $blocks$ Blöcke von dir entfernt."
lobby:
  countdown: "Das Spiel startet in $sec$ Sekunde(n)!"
  countdowncancel: "Mehr Spieler benötigt. Countdown wurde abgebrochen!"
  cancelcountdown:
    not_enough_players: "Mehr Spieler benötigt. Countdown wurde abgebrochen!"
    not_enough_teams: "Mehr Teams benötigt. Countdown wurde abgebrochen!"
  cancelstart:
    not_enough_players: "Es werden mehr Spieler benötigt, um das Spiel zu starten!"
    not_enough_teams: "Es werden mehr Teams benötigt, um das Spiel zu starten!"
  chooseteam: "Wähle ein Team"
  startgame: "Spiel starten"
  reduce_countdown: "Countdown reduzieren"
  gamefull: "Das Spiel ist voll!"
  gamefullpremium: "Das Spiel ist voll von Premium-Spielern!"
  teamjoined: "Du bist erfolgreich dem Team $team$ beigetreten!"
  leavegame: "Spiel verlassen"
  playerjoin: "$player$ ist dem Spiel beigetreten!"
  kickedbyvip: "Du wurdest von einem VIP-Spieler rausgeworfen, der dem vollen Spiel beigetreten ist."
  moreplayersneeded: "Es werden noch $count$ weitere Spieler benötigt."
  moreplayersneeded-one: "Es wird noch $count$ weiterer Spieler benötigt."
  moreteamsneeded: "Es werden mindestens zwei Teams benötigt!"
sign:
  firstline: "&6[Bedwars]"
  players: "Spieler"
  gamestate:
    stopped: "&4Gestoppt!"
    waiting: "&aWarten ..."
    running: "&9Gestartet!"
    full: "Voll!"
stats:
  header: "Bedwars-Statistik"
  kd: "K/D"
  statsnotfound: "Statistik von $player$ nicht gefunden!"
  name: "Name"
  kills: "Kills"
  deaths: "Tode"
  wins: "Gewonnen"
  loses: "Verloren"
  score: "Punkte"
  destroyedBeds: "zerstörte Betten"
  games: "Spiele"
commands:
  addgame:
    name: "Spiel hinzufügen"
    desc: "Fügt ein neues Spiel hinzu"
  addteam:
    name: "Team hinzufügen"
    desc: "Fügt ein neues Team zu einem bestimmten Spiel hinzu"
  join:
    name: "Spiel beitreten"
    desc: "Einem bestimmten Spiel beitreten"
  leave:
    name: "Spiel verlassen"
    desc: "Aktuelles Spiel verlassen"
  save:
    name: "Spiel speichern"
    desc: "Speichert die gewählte Region und Spieleinstellungen"
  settarget:
    name: "Ziel-Block setzen"
    desc: "Setzt den Respawn-Block (Ziel) eines bestimmten Teams"
  setbed:
    name: "Ziel-Block setzen (Synonym für settarget)"
    desc: "Setzt den Respawn-Block (Ziel) eines bestimmten Teams (Synonym für settarget)"
  setlobby:
    name: "Lobby setzen"
    desc: "Setzt die Position der Lobby"
  setregion:
    name: "Setzt Regionspunkt"
    desc: "Setzt einen der zwei Regionspunkte für ein bestimmtes Spiel"
  setspawn:
    name: "Teamspawn setzen"
    desc: "Setzt den Spawnpunkt eines bestimmten Teams"
  setspawner:
    name: "Spawner setzen"
    desc: "Setzt an der aktuellen Position einen Ressourcen-Spawner"
  start:
    name: "Spiel starten"
    desc: "Startet ein Spiel (Lobbymodus)"
  stop:
    name: "Spiel stoppen"
    desc: "Stoppt ein Spiel und kickt alle Spieler"
  help:
    name: "Hilfe"
    desc: "Zeigt Hilfe zu Kommandos von Bedwars an"
  reload:
    name: "Neu laden"
    desc: "Lädt die Konfiguration und Übersetzungen neu"
  setmainlobby:
    name: "Main Lobby setzen"
    desc: "Setzt die Main Lobby eines Spiels (notwendig wenn mainlobby-enabled auf true gesetzt)"
  list:
    name: "Spiele auflisten"
    desc: "Listet alle verfügbaren Spiele auf!"
  regionname:
    name: "Region-Name setzen"
    desc: "Setzt einen individuellen Namen (statt Weltnamen) für die Region"
  removeteam:
    name: "Team entfernen"
    desc: "Entfernt ein Team vom Spiel (Nur wenn das Spiel gestoppt ist)"
  removegame:
    name: "Spiel entfernen"
    desc: "Entfernt das Spiel und alle Konfigurationen"
  clearspawner:
    name: "Spawner löschen"
    desc: "Entfernt alle Ressourcen-Spawner vom Spiel"
  gametime:
    name: "Spiel-Zeit setzen"
    desc: "Setzt die Spiel-Zeit, die auf der Spielwelt verwendet werden soll."
  stats:
    name: "Statistik"
    desc: "Zeigt deine Statistik an."
  setbuilder:
    name: "Setzt Map-Info Bauer"
    desc: "Setzt den Bauer der Map eines bestimmten Spiels, der im Title beim Start des Spiels angezeigt wird."
  setgameblock:
    name: "Spiel-lock setzen"
    desc: "Setzt den Respawn Block-Typ. Dies überschreibt die 'game-block' config für dieses eine Spiel. Schreibe 'DEFAULT' als Typ, um den Typ aus der Config zu verwenden"
  setautobalance:
    name: "Autobalance setzen"
    desc: "Wenn 'global-autobalance' auf 'false' gestellt, kann man hiermit das Autobalancing pro Spiel setzen"
  setminplayers:
    name: "Mindestspielerzahl setzen"
    desc: "Setzt die Anzahl der zum Start des Spiels benötigten Spieler"
  kick:
    name: "Spieler kicken"
    desc: "Kickt einen Spieler aus dem aktuellen Spiel"
  addteamjoin:
    name: "Team-Auswahl hinzufügen"
    desc: "Markiert eine Kreatur, die zur Team-Auswahl für ein bestimmtes Team verwendet werden kann"
  addholo:
    name: "Hologram-Position hinzufügen"
    desc: "Eine Hologram-Statistik wird auf die aktuelle, stehende Position hinzugefügt"
  removeholo:
    name: "Hologram-Position entfernen"
    desc: "Sobald der Command ausgelöst wurde, kann innerhalb von 10 Sekunden auf das entsprechende Hologram geklickt werden, um es zu entfernen"
    explain: "Mache nun einen Linksklick auf das Hologram das entfernt werden soll. Innerhalb von 10 Sekunden."
  debugpaste:
    name: "Debug-Daten senden"
    desc: "Lade einige Debug-Informationen auf Hastebin hoch und erhalte einen Link, den du mit den Entwicklern teilen kannst"
  itemspaste:
    name: "Inventar in Datei speichern"
    desc: "Lade dein aktuelles Inventar in serialisierter Form auf Hastebin hoch und schaue dir an, wie diese Items für deine shop.yml aussehen"
