---
default:
  pages: "第 $current$ 頁 / 共 $max$ 頁"
  currently: 當前
errors:
  argumentslength: "參數不正確!"
  holodependencynotfound: "全息顯示前置插件缺失 $dependency$"
  packagenotfound: "無法獲得$package$ 包!"
  classnotfound: "無法獲得包 $package$ 的類$class$!"
  gameexists: "遊戲名字已經存在!"
  gamenotfound: "遊戲 '$game$' 不存在!"
  nofreegames: "暫時沒有可以加入的遊戲。"
  gamenotfoundsimple: "找不到遊戲！"
  playeramount: "玩家最少需要1名最大不超過24名!"
  teamcolornotallowed: "隊伍顏色有誤"
  teamnamelength: "隊伍名字2-20個字符"
  teamnotfound: "找不到該團隊!"
  notingame: "你不在遊戲中!"
  bedtargeting: "請指定或站在要配置的目標方塊上!"
  regionargument: "地點必須是 'loc1'或 'loc2'!"
  spawnerargument: "資源參數必須是一個有效的配置!"
  blockdownnotfound: "在你所站的地方沒有發現方塊"
  gamenotrunning: "遊戲尚未運行!"
  bungeenoserver: "Bungeecord服務器設置不准確!請聯繫管理員!"
  cantstartagain: "遊戲運行中! 你不能再次開始這個遊戲!"
  startoutofwaiting: "遊戲處於等待模式!"
  cantjoingame: "你不能加入運行的遊戲或已經開始的遊戲!"
  lobbyongameworld: "大廳不能設置到遊戲場地!"
  gamenotloaded: "遊戲無法開始!"
  gameloaderror: "載入遊戲'$game$'的時候出現一個錯誤!"
  regionnotfound: "區域文件不存在!"
  savesign: "牌子文件不能創建!"
  nogames: "找不到遊戲!"
  notenoughress: "你沒有足夠的資源購買這個物品!"
  teamnameinuse: "隊伍名字已經被使用!"
  minplayersmustnumber: "最小玩家數必須是一個數字!"
  toolongregionname: "區域名字最大15個字符!"
  notwhilegamerunning: "遊戲運行前不能這麼做!"
  notwhileingame: "在遊戲裡面不能這麼做!"
  timeincorrect: "時間數據 (0 ... 23000), '白天' 或者'夜晚'!"
  minplayersnumeric: "最小玩家必須是數字!"
  notinair: "你不在空氣中!"
  teamfull: "該團隊已滿，請重新打開團隊選擇菜單!"
  novalidmaterial: "方塊類型不正確!"
  wrongvalueonoff: "參數錯誤! Use true,on,1 to turn on - Use false,off,0 to turn off!"
  playernotfound: "玩家不在線!"
  notingameforkick: "你只能在遊戲中踢出玩家!"
  playernotingame: "玩家不再遊戲中!"
  mustbeinlobbyworld: "必須處於遊戲大廳"
  addteamjoincancel: "添加隊伍取消!"
  entitynotcompatible: "實體與隊伍不兼容!"
success:
  gameadded: "新遊戲'$game$'成功添加!"
  teamadded: "成功添加團隊 '$team$'!"
  joined: "成功加入遊戲!"
  left: "離開遊戲!"
  saved: "遊戲成功保存!"
  bedset: "成功設置$team$!隊重生點"
  regionset: "成功為$game$設置區域$location$!"
  spawnset: " $team$隊出生點成功設置!"
  spawnerset: "$name$的資源刷新點添加成功!"
  stopped: "已成功停止遊戲!"
  lobbyset: "成功設置大堂!"
  gameloaded: "成功載入了遊戲 '$game$'!"
  reloadconfig: "重新載入成功!"
  teamremoved: "隊伍已成功移除!"
  gameremoved: "遊戲已成功移除!"
  spawnercleared: "全部資源刷新點移除!"
  gamerun: "你已經開始了遊戲，現在玩家可以加入!"
  timeset: "成功設置遊戲時間!"
  regionnameset: "區域名字設置成功!"
  minplayersset: "最小玩家設置成功!"
  mainlobbyset: "主大廳設置成功!"
  gametimeset: "遊戲時間設置成功!"
  materialset: "重生點方塊設置成功!"
  builderset: "地圖作者已經成功設置，將會顯示地圖作者名字"
  autobalanceseton: "自動排行打開!"
  autobalancesetoff: "自動排行關閉!"
  selectteamjoinentity: "右鍵選擇用來加入隊伍的實體物品!"
  teamjoinadded: "隊伍 $team$的選擇加入實體設置成功"
  holoremoved: "全息統計移除!"
gamecheck:
  LOC_NOT_SET_ERROR: "區域的位置沒有設置正確!"
  TEAM_SIZE_LOW_ERROR: "你需要設置更多隊伍!"
  NO_RES_SPAWNER_ERROR: "你沒有設置任何資源刷新點!"
  NO_LOBBY_SET: "你還未設定一個大堂!"
  TEAMS_WITHOUT_SPAWNS: "有一個隊伍沒有設置出生點!"
  NO_ITEMSHOP_CATEGORIES: "商店沒有物品!"
  NO_MAIN_LOBBY_SET: "你沒有設置主大廳即使你設置'tomainlobby' 為true"
  TEAM_NO_WRONG_BED: "有隊伍沒有設置床!"
  TEAM_NO_WRONG_TARGET: "有隊伍沒有設置重生點!"
ingame:
  team: "陣營"
  teams: "團隊"
  all: "全部"
  record: "&e$record$&a 是這個地圖的記錄!"
  record-with-holders: '&a這個地圖的記錄是&e$record$&a記錄者: $holders$'
  newrecord: '&a$team$&a隊設置一個新的記錄: &6$record$'
  record-nobeddestroy: "&c記錄不能保存, 因為沒有床被摧毀!"
  teamwon: "恭喜! 隊伍 $team$ 獲勝!"
  draw: "遊戲以平局結束!"
  serverrestart: "服務器將在$sec$ 秒後重置地圖!"
  gamestarting: "遊戲正在開始..."
  gamestarted: "遊戲 '$game$' 剛剛開始了!"
  backtolobby: "$sec$ 秒後回到大堂!"
  spectator: "旁觀者"
  spectate: "&a旁觀"
  teamchest: "隊伍箱子"
  noturteamchest: "這個箱子不是你隊伍的箱子!"
  protectionleft: "將會有&c$length$&f秒無敵時間!"
  protectionend: "無敵時間結束，小心行事!"
  team-dead: "隊伍$team$被消滅!"
  no-friendlybreak: "&chi，不能破壞隊伍成員腳下的方塊!"
  teamchestdestroy: "&c有一個隊伍箱子被破壞!"
  title:
    map-builder: "由 $builder$ 建造"
    win-title: "&6恭喜!"
    win-subtitle: "$team$&6隊獲得勝利，用時&e$time$"
  shop:
    name: "物品商店"
    fullstackpershift: "按住Shift點擊獲取多組物品"
    onestackpershift: "按住Shift點擊獲取一組物品"
  player:
    left: "玩家 $player$ 已退出遊戲!"
    died: "$player$ 死亡!"
    killed: "$killer$ 殺了 $player$!"
    kicked: "$player$ 被踢出!"
    waskicked: "你從這個遊戲被踢出!"
  blocks:
    ownbeddestroy: "你不能破壞自己的床!"
    beddestroyed: "$player$破壞了 $team$的床！"
  specials:
    rescue-platform:
      left: "需要&c$time$秒&f 你才能使用下一個救援平台!"
    arrow-blocker:
      start: "箭矢傷害保護開啟，剩餘時間 &3 $time$ &f秒(s)"
      end: "箭矢傷害保護時間已結束"
      left: "箭矢保護冷卻中，剩餘時間 &3$time$ &f秒(s)"
    trap:
      trapped: "&e有人進入了你隊伍的&4陷阱"
    protection-wall:
      left: "需要&c$time$&f 秒你才能使用下一個保護牆!"
      not-usable-here: "你不能在這裡使用保護牆!"
    warp-powder:
      cancelled: "&c你的傳送被取消!"
      start: "&c$time$&f 秒後你將被傳送，請不要移動!"
      cancel: "&4取消傳送"
      multiuse: "&c你已經開始了一個傳送!"
    tntsheep:
      no-target-found: "沒有找到目標玩家!"
    tracker:
      no-target-found: "沒有找到目標玩家!"
      target-found: "$player$ 是$blocks$方塊遠離你."
lobby:
  countdown: "遊戲將在 $sec$ 秒後開始!"
  countdowncancel: "需要更多玩家。倒計時被取消了!"
  cancelcountdown:
    not_enough_players: "需要更多的玩家。取消倒計時!"
    not_enough_teams: "需要更多的團隊。取消倒計時!"
  cancelstart:
    not_enough_players: "需要更多的玩家才能開始遊戲!"
    not_enough_teams: "需要更多的團隊才能開始遊戲!"
  chooseteam: "選擇一個團隊"
  startgame: "開始遊戲"
  reduce_countdown: "減少倒計時"
  gamefull: "遊戲已滿!"
  gamefullpremium: "遊戲人數已滿"
  teamjoined: "您成功加入了團隊 $team$"
  leavegame: "離開遊戲"
  playerjoin: "玩家 $player$ 加入了遊戲!"
  kickedbyvip: "有 Vip 玩家加入的關係，你已被踢出本遊戲。"
  moreplayersneeded: "還需要 $count$ 個玩家。"
  moreplayersneeded-one: "還需要 $count$ 個玩家."
  moreteamsneeded: "至少需要有兩名玩家在兩個不同的團隊才能開始遊戲!"
sign:
  firstline: "&6[Bedwars]"
  players: "玩家"
  gamestate:
    stopped: "&4停止!"
    waiting: "&a等待中 ..."
    running: "&9正在運行!"
    full: "已滿!"
stats:
  header: "Bedwars 統計"
  kd: "K/D"
  statsnotfound: "找不到 $player$ 的統計!"
  name: "名字"
  kills: "擊殺"
  deaths: "死亡數"
  wins: "勝利數"
  loses: "失敗"
  score: "分數"
  destroyedBeds: "破壞床"
  games: "遊戲"
commands:
  addgame:
    name: "添加遊戲"
    desc: "添加一個新遊戲"
  addteam:
    name: "添加隊伍"
    desc: "添加一個隊伍到一個特定的遊戲"
  join:
    name: "加入遊戲"
    desc: "加入一個特定的遊戲"
  leave:
    name: "離開遊戲"
    desc: "離開當前遊戲"
  save:
    name: "儲存遊戲"
    desc: "保存遊戲地圖和配置文件"
  settarget:
    name: "設定目標"
    desc: "設置一個目標陷進"
  setbed:
    name: "設置床 (synonym for settarget)"
    desc: "為隊伍設置床 (Synonym for settarget)"
  setlobby:
    name: "設置大堂"
    desc: "設置遊戲大堂的位置"
  setregion:
    name: "設置區域點"
    desc: "為指定遊戲設置遊戲區域點"
  setspawn:
    name: "設置隊伍出生點"
    desc: "為隊伍設置出生點"
  setspawner:
    name: "設置資源刷新點"
    desc: "設置指定的資源刷新點"
  start:
    name: "開始遊戲"
    desc: "開始一個遊戲"
  stop:
    name: "停止遊戲"
    desc: "停止一個遊戲"
  help:
    name: "顯示幫助"
    desc: "顯示關於這個插件的所有指令"
  reload:
    name: "重載"
    desc: "重新載入配置文件和語言文件"
  setmainlobby:
    name: "設置主要的大堂"
    desc: "設置遊戲主大廳 (is needed when mainlobby-enabled is set to true)"
  list:
    name: "列出遊戲"
    desc: "列出所有可用的遊戲"
  regionname:
    name: "設置區域名字"
    desc: "給區域設置單獨的名字 (instead of world name)"
  removeteam:
    name: "移除團隊"
    desc: "從遊戲移除一個隊伍 (only in stopped mode)"
  removegame:
    name: "移除遊戲"
    desc: "從配置文件刪除遊戲"
  clearspawner:
    name: "清除資源刷新點"
    desc: "移除所有刷新點，需要保存."
  gametime:
    name: "設置遊戲時間"
    desc: "設置遊戲時間"
  stats:
    name: "統計"
    desc: "顯示你的統計"
  setbuilder:
    name: "設置地圖建造者"
    desc: "設置地圖作者，將會在遊戲結束的時候顯示."
  setgameblock:
    name: "設置遊戲方塊"
    desc: "設置遊戲的方塊類型將會替代配置文件裡面的 'game-block' . 輸入'DEFAULT' 使用默認設置"
  setautobalance:
    name: "設置自動排行"
    desc: "如果'global-autobalance'設置為'false', 使用這個指令將會設​​置為'true'!"
  setminplayers:
    name: "設置最小玩家數"
    desc: "設置遊戲開始時需要的玩家數量"
  kick:
    name: "踢出玩家"
    desc: "將指定玩家從當前遊戲踢出!"
  addteamjoin:
    name: "添加隊伍選擇"
    desc: "在當前位置加入指定隊伍的標記!"
  addholo:
    name: "Add hologram location"
    desc: "A hologram statistic will be added at the current position!"
  removeholo:
    name: "刪除權限顯示"
    desc: "使用這個命令後玩家right-click即可移除全息圖."
    explain: "10秒只可執行一次"
  debugpaste:
    name: "Paste debug data"
    desc: "This will send some debug data to hastebin and returns a link you can share with the developers"
  itemspaste:
    name: "Paste your inventory to a file"
    desc: "This will return a link where you can see your current inventory being serialized as an example for your shop.yml"
