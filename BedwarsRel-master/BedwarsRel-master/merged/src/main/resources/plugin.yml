name: "BedwarsRel"
description: |
  §aBedwars Reloaded - The Minecraft Bedwars Plugin!
  §7Bedwars is a Minecraft minigame where teams (max. 15) try to destroy the other teams' beds. But there is more: On the whole map there are ressource spawners spawning items. Some items are more worth than others and with these you can buy different things in the so called "Villager Shop". You will find powerful weapons or potions or simple blocks to get to the other bases. Get to the others bases? Yes, every team starts on an island and you have to get to the others with blocks which you can buy in the "Villager Shop". When you meet a enemy you have to try to kill him so he'll lose all the equipment which he had in his inventory. As soon as a team's bed is destroyed they cannot respawn again and last team standing wins.
author: "The BedwarsRel-Team | www.github.com/orgs/BedwarsRel/people"
website: "www.github.com/BedwarsRel"

main: io.github.bedwarsrel.BedwarsRel
version: ${project.version}
api-version: '1.20'

awareness:
    - !@UTF8

depend: []
softdepend: [Multiverse-Core, Multiworld, MultiWorld, HolographicDisplays, HologramAPI, UltimateCore]

commands:
    bw:
        description: Base command for all Test-Plugin commands
        usage: /<command> help
        permission: bw.base
    bw setspawner:
        description: Sets a ressource spawn point of a specific type
        usage: /<command> [game] [Ressource]
        permission: bw.setup
    bw addgame:
        description: Add a new game
        usage: /<command> [Name]
        permission: bw.setup
    bw start:
        description: Starts a game
        usage: /<command> [game]
        permission: bw.setup
    bw addteam:
        description: Adds a team to a game
        usage: /<command> [Name] [Color]
        permission: bw.setup
    bw save:
        description: Saves a game to config files
        usage: /<command> [Name]
        permission: bw.setup
    bw setregion:
        description: Sets one of the two possible region of the game
        usage: /<command> [Name] [loc1;loc2]
        permission: bw.setup
    bw join:
        description: Joins a specific team in a game
        usage: /<command> [game] [Team]
        permission: bw.base
    bw setspawn:
        description: Sets the spawn of a team
        usage: /<command> [game] [Team]
        permission: bw.setup
    bw setlobby:
        description: Sets the lobby location of a game
        usage: /<command> [game]
        permission: bw.setup
    bw settarget:
        description: Sets the bed of a team in a game
        usage: /<command> [game] [Team]
        permission: bw.setup
    bw setbed:
        description: Sets the bed of a team in a game
        usage: /<command> [game] [Team]
        permission: bw.setup
    bw leave:
        description: Leaves the current game
        usage: /<command>
        permission: bw.base
    bw reload:
        description: Reloads the configurations and translations
        usage: /<command>
        permission: bw.setup
    bw list:
        description: List all availabe games
        usage: /<command>
        permission: bw.base
    bw regionname:
        description: Sets a region name
        usage: /<command>
        permission: bw.setup
    bw removeteam:
        description: Removes a team of the game
        usage: /<command>
        permission: bw.setup
    bw removegame:
        description: Removes a game
        usage: /<command>
        permission: bw.setup
    bw clearspawner:
        description: Removes all ressource spawners from the game
        usage: /<command>
        permission: bw.setup
    bw gametime:
        description: Sets the time for the game used in the world
        usage: /<command>
        permission: bw.setup
    bw stats:
        description: Shows the stats of bedwars
        usage: /<command>
        permission: bw.base
    bw setminplayers:
        description: Sets the min player amount for a game
        usage: /<command>
        permission: bw.setup
    bw setgameblock:
        description: Sets the target block for a specific game
        usage: /<command>
        permission: bw.setup
    bw setbuilder:
        description: Sets the map-builder of the map for displaying title when game starts
        usage: /<command>
        permission: bw.setup
    bw setautobalance:
        description: Turn the autobalance for this game on or off when global-autobalance is false
        usage: /<command>
        permission: bw.setup
    bw kick:
        description: Kicks a player from the game
        usage: /<command>
        permission: bw.kick
    bw addteamjoin:
        description: Mark a creature which can be used to join a specific team
        usage: /<command>
        permission: bw.setup
    bw addholo:
        description: Adds a location of the hologram stats
        usage: /<command>
        permission: bw.setup
    bw removeholo:
        description: Removes a hologram statistic location
        usage: /<command>
        permission: bw.setup
    bw setvillager:
        description: Sets a villager shop at current location
        usage: /<command> [game]
        permission: bw.setup
    bw debugbed:
        description: Debug bed detection issues
        usage: /<command> [game]
        permission: bw.setup
permissions:
    bw.base:
        description: Allows you to show to play games
        default: true
    bw.setup:
        description: Allows you to set up new or existing games
        default: op
    bw.kick:
        description: Allows a player to kick other players from the game
        default: op
    bw.vip.*:
        description: Allows special user permissions
        default: op
        children:
            bw.vip.joinfull: true
            bw.vip.forcestart: true
            bw.vip.reducecountdown: true
            bw.vip.skipbalance: true
    bw.cmd:
        description: Allows to execute non-bedwars commands ingame
        default: op
    bw.otherstats:
        description: Allows to show stats of other players
        default: op
