@echo off
echo ========================================
echo BedwarsRel 1.20 汉化版构建脚本
echo ========================================
echo.

echo [1/4] 清理旧的构建文件...
if exist "target" rmdir /s /q "target"

echo [2/4] 检查依赖文件...
if not exist "../lib/HolographicDisplaysAPI_v2.1.7.jar" (
    echo 警告: 未找到 HolographicDisplaysAPI_v2.1.7.jar
    echo 请确保 lib 目录中包含必要的依赖文件
)

echo [3/4] 开始Maven构建...
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo 错误: Maven构建失败!
    pause
    exit /b 1
)

echo [4/4] 构建完成!
echo.

echo ========================================
echo 构建成功完成!
echo ========================================
echo.

if exist "target\BedwarsRel-1.20-1.3.6-汉化版.jar" (
    echo 生成的文件: target\BedwarsRel-1.20-1.3.6-汉化版.jar
    echo 文件大小: 
    dir "target\BedwarsRel-1.20-1.3.6-汉化版.jar" | findstr "BedwarsRel"
) else (
    echo 警告: 未找到生成的JAR文件
)

echo.
echo 部署说明:
echo 1. 将JAR文件复制到服务器的 plugins 目录
echo 2. 重启服务器
echo 3. 使用 /bw 命令测试功能
echo.
echo 汉化特色:
echo - 完整的中文界面和配置
echo - 默认语言设置为中文
echo - 商店界面完全汉化
echo - 配置文件注释中文化
echo - 1.20完全兼容
echo.
pause
