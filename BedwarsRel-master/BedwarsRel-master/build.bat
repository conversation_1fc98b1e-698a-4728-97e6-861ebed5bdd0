@echo off
echo ========================================
echo BedwarsRel 1.20 构建脚本
echo ========================================
echo.

echo [1/4] 清理旧的构建文件...
if exist "build" rmdir /s /q "build"
mkdir "build"

echo [2/4] 编译 Common 模块...
cd common
call mvn clean package -q -DskipTests
if %errorlevel% neq 0 (
    echo 错误: Common模块编译失败!
    pause
    exit /b 1
)
cd ..

echo [3/4] 编译 v1_20_r1 模块...
cd v1_20_r1
call mvn clean package -q -DskipTests
if %errorlevel% neq 0 (
    echo 错误: v1_20_r1模块编译失败!
    pause
    exit /b 1
)
cd ..

echo [4/4] 复制构建文件...
copy "common\target\BedwarsRel-Common-1.3.6.jar" "build\"
copy "v1_20_r1\target\BedwarsRel-v1_20_r1-1.3.6.jar" "build\"

echo.
echo ========================================
echo 构建完成!
echo ========================================
echo 生成的文件位于 build 目录:
echo - BedwarsRel-Common-1.3.6.jar (核心插件)
echo - BedwarsRel-v1_20_r1-1.3.6.jar (1.20适配)
echo.
echo 部署说明:
echo 1. 将两个JAR文件都放入服务器的 plugins 目录
echo 2. 确保服务器版本为 Minecraft 1.20.1
echo 3. 推荐使用 Paper 或 Spigot 服务端
echo 4. 可选: 安装 HolographicDisplays 插件以启用全息图功能
echo.
pause
