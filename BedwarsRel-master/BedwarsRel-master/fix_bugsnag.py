#!/usr/bin/env python3
"""
批量修复BedwarsRel项目中的Bugsnag依赖问题
"""

import os
import re

def fix_bugsnag_calls(file_path):
    """修复单个文件中的Bugsnag调用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换所有的getBugsnag().notify()调用
        content = re.sub(
            r'BedwarsRel\.getInstance\(\)\.getBugsnag\(\)\.notify\([^)]+\);',
            lambda m: m.group(0).replace('getBugsnag().notify', 'logError'),
            content
        )
        
        # 替换更复杂的模式
        content = re.sub(
            r'\.getBugsnag\(\)\.notify\(([^)]+)\)',
            r'.logError(\1)',
            content
        )
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed: {file_path}")
            return True
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False
    
    return False

def main():
    """主函数"""
    base_dir = "common/src/main/java"
    fixed_count = 0
    
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                if fix_bugsnag_calls(file_path):
                    fixed_count += 1
    
    print(f"Total files fixed: {fixed_count}")

if __name__ == "__main__":
    main()
